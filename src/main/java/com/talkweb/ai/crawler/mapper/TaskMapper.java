package com.talkweb.ai.crawler.mapper;

import com.talkweb.ai.crawler.dto.CreateTaskRequest;
import com.talkweb.ai.crawler.dto.TaskDto;
import com.talkweb.ai.crawler.model.Task;
import com.talkweb.ai.crawler.model.TaskStatus;
import org.springframework.stereotype.Component;

/**
 * Mapper for converting between Task entities and DTOs.
 */
@Component
public class TaskMapper {

    public Task toEntity(CreateTaskRequest request) {
        if (request == null) {
            return null;
        }
        return Task.builder()
                .url(request.getUrl())
                .maxDepth(request.getMaxDepth())
                .outputDir(request.getOutputDir())
                .allowedDomains(request.getAllowedDomains() != null ? String.join(",", request.getAllowedDomains()) : null)
                .maxConnections(request.getMaxConnectionsPerDomain())
                .enableDynamic(request.isEnableDynamicFetcher())
                .callbackUrl(request.getCallbackUrl())
                .status(TaskStatus.PENDING)
                .progress(0)
                .build();
    }

    public TaskDto toDto(Task task) {
        if (task == null) {
            return null;
        }
        TaskDto dto = new TaskDto();
        dto.setTaskId(task.getId());
        dto.setUrl(task.getUrl());
        dto.setStatus(task.getStatus());
        dto.setProgress(task.getProgress());
        dto.setStartTime(task.getStartTime());
        dto.setEndTime(task.getEndTime());
        dto.setCreatedAt(task.getCreatedAt());
        dto.setUpdatedAt(task.getUpdatedAt());
        return dto;
    }
}
