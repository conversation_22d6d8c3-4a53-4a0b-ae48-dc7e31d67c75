package com.talkweb.ai.crawler;

import com.talkweb.ai.crawler.core.CrawlerMain;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * Main application class for the web crawler.
 * This serves as the entry point for the crawler application.
 * Now implemented as a Spring Boot application for better integration and configuration.
 */
@Component
@ConditionalOnProperty(name = "crawler.cli.enabled", havingValue = "true", matchIfMissing = true)
public class CrawlerCli implements CommandLineRunner {

    @Value("${application.name:AI Web Crawler}")
    private String appName;

    @Value("${application.version:1.0.0}")
    private String version;

    private static CrawlerMain crawler;

    /**
     * Main method to start the Spring Boot crawler application.
     *
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        // 不再作为Spring Boot主类运行
        new CrawlerCli().run(args);
    }

    /**
     * CommandLineRunner bean that executes the crawler logic when the Spring application starts.
     * This approach allows Spring to handle application lifecycle while still providing console-based execution.
     *
     * @return A CommandLineRunner for executing crawler logic
     */
    @Override
    public void run(String... args) {
        System.out.println("Starting " + appName + " v" + version);
        System.out.println("Initializing crawler components...");

        // Register shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (crawler != null) {
                crawler.shutdown();
            }
            System.out.println("Crawler has been stopped.");
        }));

        try {
            // Initialize and start the crawler with command line arguments
            crawler = new CrawlerMain(args);
            crawler.start();

            System.out.println("Crawler started successfully!");
            System.out.println("Press Ctrl+C to stop the crawler...");

            // Keep the thread alive until application shutdown
            Thread.currentThread().join();
        } catch (Exception e) {
            if (e instanceof InterruptedException) {
                System.out.println("Crawler was interrupted.");
                Thread.currentThread().interrupt();
            } else {
                System.err.println("Error in crawler: " + e.getMessage());
                e.printStackTrace();
                System.exit(1);
            }
        }
    }
}
