package com.talkweb.ai.crawler.converter;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * HTML到Markdown的转换器，负责将HTML内容转换为Markdown格式。
 */
public class HtmlToMarkdownConverter {
    
    /**
     * 将HTML文档转换为Markdown格式字符串。
     * @param doc HTML文档
     * @return Markdown格式字符串
     */
    public String convert(Document doc) {
        StringBuilder markdown = new StringBuilder();
        
        // 获取标题
        Element titleElement = doc.selectFirst("title");
        if (titleElement != null) {
            markdown.append("# ").append(titleElement.text()).append("\n\n");
        }
        
        // 获取正文内容
        Element body = doc.body();
        if (body != null) {
            markdown.append(convertElement(body));
        }
        
        return markdown.toString();
    }
    
    /**
     * 递归转换HTML元素为Markdown格式。
     * @param element HTML元素
     * @return Markdown格式字符串
     */
    private String convertElement(Element element) {
        StringBuilder markdown = new StringBuilder();
        String tag = element.tagName();
        
        switch (tag) {
            case "h1":
                markdown.append("# ").append(element.text()).append("\n\n");
                break;
            case "h2":
                markdown.append("## ").append(element.text()).append("\n\n");
                break;
            case "h3":
                markdown.append("### ").append(element.text()).append("\n\n");
                break;
            case "h4":
                markdown.append("#### ").append(element.text()).append("\n\n");
                break;
            case "h5":
                markdown.append("##### ").append(element.text()).append("\n\n");
                break;
            case "h6":
                markdown.append("###### ").append(element.text()).append("\n\n");
                break;
            case "p":
                markdown.append(element.text()).append("\n\n");
                break;
            case "ul":
                for (Element child : element.children()) {
                    if (child.tagName().equals("li")) {
                        markdown.append("- ").append(child.text()).append("\n");
                    }
                }
                markdown.append("\n");
                break;
            case "ol":
                int index = 1;
                for (Element child : element.children()) {
                    if (child.tagName().equals("li")) {
                        markdown.append(index).append(". ").append(child.text()).append("\n");
                        index++;
                    }
                }
                markdown.append("\n");
                break;
            case "blockquote":
                markdown.append("> ").append(element.text()).append("\n\n");
                break;
            case "pre":
            case "code":
                markdown.append("```\n").append(element.text()).append("\n```\n\n");
                break;
            case "img":
                String src = element.attr("src");
                String alt = element.attr("alt");
                String title = element.attr("title");
                String imgDesc = alt.isEmpty() ? title : alt;
                markdown.append("![")
                        .append(imgDesc)
                        .append("](")
                        .append(src)
                        .append(")")
                        .append("\n\n");
                break;
            case "table":
                markdown.append(convertTable(element));
                break;
            default:
                // 递归处理子元素
                Elements children = element.children();
                for (Element child : children) {
                    markdown.append(convertElement(child));
                }
                // 如果没有子元素，添加文本内容
                if (children.isEmpty() && !element.text().trim().isEmpty()) {
                    markdown.append(element.text()).append("\n\n");
                }
                break;
        }
        
        return markdown.toString();
    }
    
    /**
     * 将HTML表格转换为Markdown格式。
     * @param tableElement 表格元素
     * @return Markdown格式表格字符串
     */
    private String convertTable(Element tableElement) {
        StringBuilder markdown = new StringBuilder();
        Elements rows = tableElement.select("tr");
        
        if (rows.isEmpty()) {
            return "";
        }
        
        // 处理表头
        Element headerRow = rows.get(0);
        Elements headers = headerRow.select("th, td");
        for (Element header : headers) {
            markdown.append("| ").append(header.text()).append(" ");
        }
        markdown.append("|\n");
        
        // 添加分隔线
        for (int i = 0; i < headers.size(); i++) {
            markdown.append("| --- ");
        }
        markdown.append("|\n");
        
        // 处理表格内容
        for (int i = 1; i < rows.size(); i++) {
            Element row = rows.get(i);
            Elements cells = row.select("td, th");
            for (Element cell : cells) {
                markdown.append("| ").append(cell.text()).append(" ");
            }
            markdown.append("|\n");
        }
        
        markdown.append("\n");
        return markdown.toString();
    }
}