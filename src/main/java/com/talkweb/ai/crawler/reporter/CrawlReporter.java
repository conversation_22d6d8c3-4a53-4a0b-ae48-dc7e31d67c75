package com.talkweb.ai.crawler.reporter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.talkweb.ai.crawler.model.PageData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 爬取报告器，负责记录日志、异常链接清单和生成抓取统计报告。
 */
public class CrawlReporter {
    private static final Logger logger = LoggerFactory.getLogger(CrawlReporter.class);
    private static final Logger errorLogger = LoggerFactory.getLogger("ErrorLogger");
    
    private final String outputDir;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicInteger successfulPages = new AtomicInteger(0);
    private final AtomicInteger failedPages = new AtomicInteger(0);
    private final Map<String, String> errorLinks = new HashMap<>();

    /**
     * 构造函数，初始化爬取报告器。
     * @param outputDir 输出目录路径
     */
    public CrawlReporter(String outputDir) {
        this.outputDir = outputDir;
        logger.info("CrawlReporter initialized with output directory: {}", outputDir);
    }

    /**
     * 记录日志消息。
     * @param level 日志级别 (INFO, WARN, ERROR)
     * @param message 日志消息
     */
    public void log(String level, String message) {
        switch (level.toUpperCase()) {
            case "INFO":
                logger.info(message);
                break;
            case "WARN":
                logger.warn(message);
                break;
            case "ERROR":
                logger.error(message);
                errorLogger.error(message);
                break;
            default:
                logger.info(message);
        }
        
        // 写入到日志文件
        try {
            writeToLogFile(level, message);
        } catch (IOException e) {
            logger.error("Failed to write to log file", e);
        }
    }

    /**
     * 记录成功抓取的页面。
     * @param pageData 页面数据
     */
    public void recordSuccess(PageData pageData) {
        successfulPages.incrementAndGet();
        String logMessage = "成功抓取页面: " + pageData.getUrl();
        logger.info(logMessage);
        
        // 写入到日志文件
        try {
            writeToLogFile("INFO", logMessage);
        } catch (IOException e) {
            logger.error("Failed to write to log file", e);
        }
    }

    /**
     * 记录抓取失败的页面和错误信息。
     * @param url 页面URL
     * @param errorMessage 错误消息
     */
    public void recordFailure(String url, String errorMessage) {
        failedPages.incrementAndGet();
        synchronized (errorLinks) {
            errorLinks.put(url, errorMessage);
        }
        String logMessage = "抓取页面失败: " + url + ", 错误: " + errorMessage;
        logger.error(logMessage);
        errorLogger.error(logMessage);
        
        // 写入到日志文件和错误文件
        try {
            writeToLogFile("ERROR", logMessage);
            writeToErrorFile(url, errorMessage);
        } catch (IOException e) {
            logger.error("Failed to write to log/error files", e);
        }
    }

    /**
     * 生成抓取统计报告。
     * @throws IOException 如果写入报告文件时发生IO错误
     */
    public void generateReport() throws IOException {
        Map<String, Object> report = new HashMap<>();
        report.put("timestamp", System.currentTimeMillis());
        report.put("successfulPages", successfulPages.get());
        report.put("failedPages", failedPages.get());
        report.put("errorLinks", errorLinks);
        
        Path reportPath = Paths.get(outputDir, "crawl_report.json");
        objectMapper.writeValue(reportPath.toFile(), report);
        logger.info("抓取统计报告已生成: {}", reportPath);
    }
    
    /**
     * 写入日志到文件。
     * @param level 日志级别
     * @param message 日志消息
     * @throws IOException 如果写入失败
     */
    private void writeToLogFile(String level, String message) throws IOException {
        Path logPath = Paths.get(outputDir, "crawl_log.jsonl");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String logEntry = String.format("{\"timestamp\":\"%s\",\"level\":\"%s\",\"message\":\"%s\"}\n", 
                                      timestamp, level, message);
        
        Files.createDirectories(logPath.getParent());
        Files.write(logPath, logEntry.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.APPEND);
    }
    
    /**
     * 写入错误到错误文件。
     * @param url 错误URL
     * @param errorMessage 错误消息
     * @throws IOException 如果写入失败
     */
    private void writeToErrorFile(String url, String errorMessage) throws IOException {
        Path errorPath = Paths.get(outputDir, "errors.log");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String errorEntry = String.format("[%s] %s: %s\n", timestamp, url, errorMessage);
        
        Files.createDirectories(errorPath.getParent());
        Files.write(errorPath, errorEntry.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.APPEND);
    }
}