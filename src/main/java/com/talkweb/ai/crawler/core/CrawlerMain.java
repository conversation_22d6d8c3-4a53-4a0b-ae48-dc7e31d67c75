package com.talkweb.ai.crawler.core;

import com.talkweb.ai.crawler.config.ConfigManager;
import com.talkweb.ai.crawler.config.CrawlerConfig;
import com.talkweb.ai.crawler.coordinator.CrawlCoordinator;
import com.talkweb.ai.crawler.metrics.MetricsExporter;

import java.io.IOException;
import java.util.Arrays;

/**
 * Main crawler implementation class.
 * This class contains the core crawling logic and coordinates
 * between different components of the crawler.
 */
public class CrawlerMain {
    private final String[] args;
    private ConfigManager configManager;
    private CrawlCoordinator crawlCoordinator;
    private MetricsExporter metricsExporter;
    private boolean _shutdownRequested = false; // Flag to indicate if shutdown is requested

    /**
     * 构造函数，接收命令行参数
     *
     * @param args 命令行参数
     */
    public CrawlerMain(String[] args) {
        this.args = args;
    }

    /**
     * 默认构造函数
     */
    public CrawlerMain() {
        this(new String[0]);
    }

    /**
     * Initializes and starts the web crawler.
     */
    public void start() {
        System.out.println("CrawlerMain: Starting web crawler...");
        start(new ConfigManager(args, null));
        // TODO: Start crawling based on configuration
        System.out.println("Crawler components initialized. Ready to crawl.");
    }

    public void start(ConfigManager manager) {
        this.configManager = manager;
        configManager.printConfig();
        try {
            if (configManager.isPrometheusEnabled() && metricsExporter == null) { // Ensure metricsExporter is initialized only once
                metricsExporter = new MetricsExporter(9090); // 使用9090端口作为Prometheus指标服务器端口
            }

            // Build CrawlerConfig from ConfigManager
            // Assuming ConfigManager has this getter
            CrawlerConfig crawlerConfig = new CrawlerConfig.Builder()
                .setStartUrl(configManager.getUrl()) // Assuming getUrl() provides the start URL
                .setAllowedDomains(Arrays.asList(configManager.getAllowedDomains()))
                .setMaxDepth(configManager.getMaxDepth())
                .setOutputDir(configManager.getOutputDirectory()) // Corrected method name
                .setMaxConnectionsPerDomain(configManager.getMaxConnectionsPerDomain()) // Assuming ConfigManager has this getter
                .setConnectionTimeout(configManager.getConnectionTimeout()) // Assuming ConfigManager has this getter
                .setReadTimeout(configManager.getReadTimeout()) // Assuming ConfigManager has this getter
                .setMaxRetries(configManager.getMaxRetries()) // Assuming ConfigManager has this getter
                .setEnableDynamicFetcher(configManager.isEnableDynamicFetcher()) // Assuming ConfigManager has this getter
                // Potentially set ParserConfig, DownloaderConfig, StorageConfig if available from ConfigManager
                .setUserAgent(configManager.getUserAgent()) // Assuming ConfigManager has this getter
                .setDeepDetectHtml(configManager.isDeepDetectHtml())
                .setMaxFileSize(configManager.getMaxFileSize())
                .build();

            crawlCoordinator = new CrawlCoordinator(crawlerConfig,
                configManager.getExternalLinkMaxDepth(),
                configManager.getThreads(),
                metricsExporter);
            crawlCoordinator.startCrawl(crawlerConfig.getStartUrl()); // Use startUrl from CrawlerConfig

            // Implement exitOnComplete logic
            if (configManager.isExitOnComplete()) {
                System.out.println("CrawlerMain: exitOnComplete is true. Monitoring for completion...");
                while (!_shutdownRequested) {
                    try {
                        Thread.sleep(10000); // Check every 10 seconds
                        if (crawlCoordinator.isEffectivelyIdle()) {
                            System.out.println("CrawlerMain: Crawl tasks completed and coordinator is idle.");
                            System.out.println("CrawlerMain: Initiating shutdown due to exitOnComplete.");
                            this.shutdown(); // Perform graceful shutdown
                            System.out.println("CrawlerMain: Shutdown complete. Exiting application.");
                            // System.exit(0); // Exit the application
                        }
                    } catch (InterruptedException e) {
                        System.out.println("CrawlerMain: Monitoring thread interrupted. Re-checking idle status before potential exit.");
                        Thread.currentThread().interrupt(); // Preserve interrupt status
                        // Optionally, re-check idle status immediately if interrupted before deciding to exit or continue loop
                        if (crawlCoordinator.isEffectivelyIdle()) {
                            System.out.println("CrawlerMain: Crawl tasks completed (checked after interrupt). Initiating shutdown.");
                            this.shutdown();
                            System.out.println("CrawlerMain: Shutdown complete. Exiting application.");
                            // System.exit(0);
                        }
                    }
                }
            }

        } catch (IOException e) {
            System.err.println("无法启动Prometheus指标服务器或初始化爬虫时出错: " + e.getMessage());
            // Fallback or error handling if necessary, for now, we'll log and potentially not start.
            // If a critical error occurs, it might be better to not proceed or re-throw.
            // For simplicity, the original fallback logic is removed as it would also fail with the new constructor.
            // Consider if a non-metrics version of CrawlCoordinator is needed or if metricsExporter can be null.
        }
    }

    /**
     * Gracefully shuts down the crawler.
     */
    public void shutdown() {
        System.out.println("CrawlerMain: Shutting down crawler...");
        if (_shutdownRequested) return;

        _shutdownRequested = true; // Set shutdown flag to true
        if (crawlCoordinator != null) {
            crawlCoordinator.awaitTermination();
            crawlCoordinator.shutdown();
        }
        if (metricsExporter != null) {
            metricsExporter.shutdown();
        }
    }
}
