package com.talkweb.ai.crawler.config;

/**
 * 解析器配置类，封装HTML解析相关参数
 */
public class ParserConfig {
    private boolean extractMetadata = true;
    private boolean preserveOriginalHtml = false;
    private int maxContentLength = 1000000; // 1MB
    private boolean skipComments = true;
    
    public boolean isExtractMetadata() {
        return extractMetadata;
    }
    
    public void setExtractMetadata(boolean extractMetadata) {
        this.extractMetadata = extractMetadata;
    }
    
    public boolean isPreserveOriginalHtml() {
        return preserveOriginalHtml;
    }
    
    public void setPreserveOriginalHtml(boolean preserveOriginalHtml) {
        this.preserveOriginalHtml = preserveOriginalHtml;
    }
    
    public int getMaxContentLength() {
        return maxContentLength;
    }
    
    public void setMaxContentLength(int maxContentLength) {
        this.maxContentLength = maxContentLength;
    }
    
    public boolean isSkipComments() {
        return skipComments;
    }
    
    public void setSkipComments(boolean skipComments) {
        this.skipComments = skipComments;
    }
}