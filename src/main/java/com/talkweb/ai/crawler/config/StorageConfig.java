package com.talkweb.ai.crawler.config;

/**
 * 存储配置类，封装数据存储相关参数
 */
public class StorageConfig {
    private String storagePath = "./data";
    private int maxCacheSize = 1000;
    private boolean enableCompression = true;
    
    public String getStoragePath() {
        return storagePath;
    }
    
    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }
    
    public int getMaxCacheSize() {
        return maxCacheSize;
    }
    
    public void setMaxCacheSize(int maxCacheSize) {
        this.maxCacheSize = maxCacheSize;
    }
    
    public boolean isEnableCompression() {
        return enableCompression;
    }
    
    public void setEnableCompression(boolean enableCompression) {
        this.enableCompression = enableCompression;
    }
}