package com.talkweb.ai.crawler.config;

import org.apache.commons.lang3.StringUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 配置文件管理类，负责加载配置文件和命令行参数，并提供配置访问接口。
 * 配置优先级：命令行参数 > 配置文件 > 默认值
 */
public class ConfigManager {
    private static final String DEFAULT_CONFIG_FILE = "crawler.properties";
    private static final Map<String, String> DEFAULT_VALUES = new HashMap<>();

    static {
        // 设置默认值
        DEFAULT_VALUES.put("url", "");
        DEFAULT_VALUES.put("maxDepth", "3");
        DEFAULT_VALUES.put("allowedDomains", "");
        DEFAULT_VALUES.put("threads", "4");
        DEFAULT_VALUES.put("output", "./output");
        DEFAULT_VALUES.put("enablePrometheus", "false");
        DEFAULT_VALUES.put("connectionTimeout", "1000"); // 10 seconds, renamed from timeout
        DEFAULT_VALUES.put("readTimeout", "1000"); // 10 seconds
        DEFAULT_VALUES.put("maxConnectionsPerDomain", "5");
        DEFAULT_VALUES.put("maxRetries", "1");
        DEFAULT_VALUES.put("enableDynamicFetcher", "false");
        DEFAULT_VALUES.put("downloadAttachments", "false");
        DEFAULT_VALUES.put("externalLinkMaxDepth", "1");
        DEFAULT_VALUES.put("exitOnComplete", "true"); // New configuration
        DEFAULT_VALUES.put("playwrightBrowserType", "chromium");
        DEFAULT_VALUES.put("deepDetectHtml", "false");

        DEFAULT_VALUES.put("playwrightHeadless", "true");
        DEFAULT_VALUES.put("playwrightTimeout", "30000"); // 30 seconds
        DEFAULT_VALUES.put("userAgent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        DEFAULT_VALUES.put("maxFileSize", "10485760"); // 10 MB
        DEFAULT_VALUES.put("timeout", "10000"); // 10 seconds, this is now effectively an alias for connectionTimeout
    }

    private final Map<String, String> config = new HashMap<>();

    /**
     * 构造函数，加载配置
     *
     * @param args       命令行参数
     * @param configFile 配置文件路径，如果为null则使用默认配置文件
     */
    public ConfigManager(String[] args, String configFile) {
        // 加载默认值
        config.putAll(DEFAULT_VALUES);

        // 加载配置文件
        loadConfigFile(configFile != null ? configFile : DEFAULT_CONFIG_FILE);

        // 解析命令行参数，覆盖配置文件中的值
        parseCommandLineArgs(args);
    }

    /**
     * 加载配置文件
     *
     * @param configFile 配置文件路径
     */
    private void loadConfigFile(String configFile) {
        Properties properties = new Properties();
        try (FileInputStream fis = new FileInputStream(configFile)) {
            properties.load(fis);
            properties.forEach((key, value) -> config.put(key.toString(), value.toString()));
            System.out.println("配置文件 " + configFile + " 加载成功。");
        } catch (IOException e) {
            System.out.println("无法加载配置文件 " + configFile + "，使用默认值。");
        }
    }

    /**
     * 解析命令行参数
     *
     * @param args 命令行参数
     */
    private void parseCommandLineArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "--url":
                    if (++i < args.length) config.put("url", args[i]);
                    break;
                case "--max-depth":
                    if (++i < args.length) config.put("maxDepth", args[i]);
                    break;
                case "--external-link-max-depth":
                    if (++i < args.length) config.put("externalLinkMaxDepth", args[i]);
                    break;
                case "--allowed-domains":
                    if (++i < args.length) config.put("allowedDomains", args[i]);
                    break;
                case "--threads":
                    if (++i < args.length) config.put("threads", args[i]);
                    break;
                case "--output":
                    if (++i < args.length) config.put("output", args[i]);
                    break;
                case "--config":
                    if (++i < args.length) loadConfigFile(args[i]);
                    break;
                case "--enable-prometheus":
                    if (++i < args.length) config.put("enablePrometheus", args[i]);
                    break;
                case "--connection-timeout": // Renamed from --timeout
                    if (++i < args.length) config.put("connectionTimeout", args[i]);
                    break;
                case "--read-timeout":
                    if (++i < args.length) config.put("readTimeout", args[i]);
                    break;
                case "--max-connections-per-domain":
                    if (++i < args.length) config.put("maxConnectionsPerDomain", args[i]);
                    break;
                case "--max-retries":
                    if (++i < args.length) config.put("maxRetries", args[i]);
                    break;
                case "--enable-dynamic-fetcher":
                    if (++i < args.length) config.put("enableDynamicFetcher", args[i]);
                    break;
                case "--download-attachments":
                    if (++i < args.length) config.put("downloadAttachments", args[i]);
                    break;
                case "--playwright-browser-type":
                    if (++i < args.length) config.put("playwrightBrowserType", args[i]);
                    break;
                case "--deep-detect-html":
                    if (++i < args.length) config.put("deepDetectHtml", args[i]);
                    break;
                case "--playwright-headless":
                    if (++i < args.length) config.put("playwrightHeadless", args[i]);
                    break;
                case "--playwright-timeout":
                    if (++i < args.length) config.put("playwrightTimeout", args[i]);
                    break;
                case "--user-agent":
                    if (++i < args.length) config.put("userAgent", args[i]);
                    break;
                case "--max-file-size":
                    if (++i < args.length) config.put("maxFileSize", args[i]);
                    break;
                case "--exit-on-complete": // New command line argument
                    if (++i < args.length) config.put("exitOnComplete", args[i]);
                    break;
                default:
                    System.out.println("未知参数: " + args[i]);
                    System.out.println("可用参数: --url url --max-depth depth --allowed-domains domains --threads count --output dir --config file --enable-prometheus true|false --connection-timeout ms --read-timeout ms --max-connections-per-domain num --max-retries num --enable-dynamic-fetcher true|false --download-attachments true|false --exit-on-complete true|false");
            }
        }
    }

    /**
     * 获取起始URL
     *
     * @return 起始URL
     */
    public String getUrl() {
        return config.get("url");
    }

    /**
     * 获取最大递归深度
     *
     * @return 最大递归深度
     */
    public int getMaxDepth() {
        try {
            return Integer.parseInt(config.get("maxDepth"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("maxDepth"));
        }
    }

    /**
     * 获取最大递归深度
     *
     * @return 最大递归深度
     */
    public int getExternalLinkMaxDepth() {
        try {
            return Integer.parseInt(config.get("externalLinkMaxDepth"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("externalLinkMaxDepth"));
        }
    }

    /**
     * 获取允许抓取的域名列表
     *
     * @return 域名列表
     */
    public String[] getAllowedDomains() {
        String domains = config.get("allowedDomains");
        return domains.isEmpty() ? new String[0] : domains.split(",");
    }

    /**
     * 获取线程数
     *
     * @return 线程数
     */
    public int getThreads() {
        try {
            return Integer.parseInt(config.get("threads"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("threads"));
        }
    }

    public String getPlaywrightBrowserType() {
        return config.getOrDefault("playwrightBrowserType", DEFAULT_VALUES.get("playwrightBrowserType"));
    }

    public boolean isPlaywrightHeadless() {
        String headless = config.get("playwrightHeadless");
        return StringUtils.equalsIgnoreCase("true", headless) || StringUtils.equalsIgnoreCase("1", headless);
    }

    public int getPlaywrightTimeout() {
        try {
            return Integer.parseInt(config.get("playwrightTimeout"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("playwrightTimeout"));
        }
    }

    public String getUserAgent() {
        return config.getOrDefault("userAgent", DEFAULT_VALUES.get("userAgent"));
    }

    public boolean isDeepDetectHtml() {
        String deepDetectHtml = config.get("deepDetectHtml");
        return StringUtils.equalsIgnoreCase("true", deepDetectHtml) || StringUtils.equalsIgnoreCase("1", deepDetectHtml);
    }

    public long getMaxFileSize() {
        try {
            return Long.parseLong(config.get("maxFileSize"));
        } catch (NumberFormatException e) {
            return Long.parseLong(DEFAULT_VALUES.get("maxFileSize"));
        }
    }

    public boolean isPrometheusEnabled() {
        String enablePrometheus = config.get("enablePrometheus");
        return StringUtils.equalsIgnoreCase("true", enablePrometheus) || StringUtils.equalsIgnoreCase("1", enablePrometheus);
    }

    public int getConnectionTimeout() { // Renamed from getTimeout
        try {
            return Integer.parseInt(config.get("connectionTimeout"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("connectionTimeout"));
        }
    }

    public int getReadTimeout() {
        try {
            return Integer.parseInt(config.get("readTimeout"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("readTimeout"));
        }
    }

    public int getMaxConnectionsPerDomain() {
        try {
            return Integer.parseInt(config.get("maxConnectionsPerDomain"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("maxConnectionsPerDomain"));
        }
    }

    public int getMaxRetries() {
        try {
            return Integer.parseInt(config.get("maxRetries"));
        } catch (NumberFormatException e) {
            return Integer.parseInt(DEFAULT_VALUES.get("maxRetries"));
        }
    }

    public boolean isEnableDynamicFetcher() {
        String enableDynamicFetcher = config.get("enableDynamicFetcher");
        return StringUtils.equalsIgnoreCase("true", enableDynamicFetcher) || StringUtils.equalsIgnoreCase("1", enableDynamicFetcher);
    }

    public int getTimeout() { // Original getTimeout, now effectively an alias or could be removed if not used elsewhere. Consider deprecating.
        try {
            // Attempt to parse 'timeout' if it exists, otherwise, perhaps return a default or throw an exception.
            // For now, returning 0 if not found or not a number, to avoid NullPointerException from DEFAULT_VALUES.get("timeout")
            String timeoutValue = config.get("timeout");
            if (timeoutValue != null) {
                return Integer.parseInt(timeoutValue);
            }
            return 0; // Or a more sensible default if 'timeout' key is expected to be sometimes absent
        } catch (NumberFormatException e) {
            return 0; // Default if parsing fails
        }
    }

    public boolean isDownloadAttachments() {
        String downloadAttachments = config.get("downloadAttachments");
        return StringUtils.equalsIgnoreCase("true", downloadAttachments) || StringUtils.equalsIgnoreCase("1", downloadAttachments);
    }

    /**
     * 获取输出目录
     *
     * @return 输出目录
     */
    public String getOutputDirectory() {
        return config.get("output");
    }

    /**
     * 是否在所有任务完成后自动退出
     *
     * @return 如果配置为true，则返回true，否则返回false
     */
    public boolean isExitOnComplete() {
        String exitOnComplete = config.get("exitOnComplete");
        return StringUtils.equalsIgnoreCase("true", exitOnComplete) || StringUtils.equalsIgnoreCase("1", exitOnComplete);
    }

    /**
     * 打印当前配置
     */
    public void printConfig() {
        System.out.println("当前配置:");
        System.out.println("起始URL: " + getUrl());
        System.out.println("最大深度: " + getMaxDepth());
        System.out.println("外部链接最大深度: " + getExternalLinkMaxDepth());
        System.out.println("允许的域名: " + Arrays.toString(getAllowedDomains()));
        System.out.println("线程数: " + getThreads());
        System.out.println("输出目录: " + getOutputDirectory());
        System.out.println("连接超时时间 (ms): " + getConnectionTimeout());
        System.out.println("读取超时时间 (ms): " + getReadTimeout());
        System.out.println("每域名最大连接数: " + getMaxConnectionsPerDomain());
        System.out.println("最大重试次数: " + getMaxRetries());
        System.out.println("启用动态抓取: " + isEnableDynamicFetcher());
        System.out.println("下载附件: " + isDownloadAttachments());
        System.out.println("完成后自动退出: " + isExitOnComplete());
        System.out.println("启用Prometheus: " + isPrometheusEnabled());
    }

    public String getOutputDir() {
        return getOutputDirectory();
    }
}
