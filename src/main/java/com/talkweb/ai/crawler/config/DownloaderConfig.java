package com.talkweb.ai.crawler.config;

/**
 * 下载器配置类，封装文件下载相关参数
 */
public class DownloaderConfig {
    private long maxFileSize = 10 * 1024 * 1024; // 10MB
    private int downloadTimeout = 30000; // 30s
    private boolean followRedirects = true;
    private String userAgent = "Mozilla/5.0";
    
    public long getMaxFileSize() {
        return maxFileSize;
    }
    
    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }
    
    public int getDownloadTimeout() {
        return downloadTimeout;
    }
    
    public void setDownloadTimeout(int downloadTimeout) {
        this.downloadTimeout = downloadTimeout;
    }
    
    public boolean isFollowRedirects() {
        return followRedirects;
    }
    
    public void setFollowRedirects(boolean followRedirects) {
        this.followRedirects = followRedirects;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
}