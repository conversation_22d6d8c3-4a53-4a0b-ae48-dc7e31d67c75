package com.talkweb.ai.crawler.config;

import org.springframework.context.annotation.Configuration;
import java.util.List;

/**
 * 爬虫全局配置类，封装所有固定参数和运行时参数
 */
@Configuration
public class CrawlerConfig {
    // 系统级固定参数
    private  int maxConnectionsPerDomain;
    private  int connectionTimeout;
    private  int readTimeout;
    private  int maxRetries;

    private  long maxFileSize; // 最大文件大小，10MB
    private  String userAgent;

    // 组件级配置
    private  ParserConfig parserConfig;
    private  DownloaderConfig downloaderConfig;
    private  StorageConfig storageConfig;

    // 运行时参数
    private  String startUrl;
    private  List<String> allowedDomains;
    private  int maxDepth;
    private  String outputDir;
    private  boolean enableDynamicFetcher;
    private  String playwrightBrowserType;
    private  boolean playwrightHeadless;
    private  int playwrightTimeout;

    private  boolean deepDetectHtml; // 是否深度检测HTML内容

    // Default constructor for Spring
    public CrawlerConfig() {
        this.maxConnectionsPerDomain = 5;
        this.connectionTimeout = 10000;
        this.readTimeout = 30000;
        this.maxRetries = 3;
        this.maxFileSize = 10 * 1024 * 1024;
        this.userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        this.parserConfig = new ParserConfig();
        this.downloaderConfig = new DownloaderConfig();
        this.storageConfig = new StorageConfig();
        this.startUrl = "";
        this.allowedDomains = List.of();
        this.maxDepth = 3;
        this.outputDir = "./output";
        this.enableDynamicFetcher = false;
        this.playwrightBrowserType = "chromium";
        this.playwrightHeadless = true;
        this.playwrightTimeout = 30000;
        this.deepDetectHtml = true;
    }

    private CrawlerConfig(Builder builder) {
        this.maxConnectionsPerDomain = builder.maxConnectionsPerDomain;
        this.connectionTimeout = builder.connectionTimeout;
        this.readTimeout = builder.readTimeout;
        this.maxRetries = builder.maxRetries;
        this.parserConfig = builder.parserConfig;
        this.downloaderConfig = builder.downloaderConfig;
        this.storageConfig = builder.storageConfig;
        this.startUrl = builder.startUrl;
        this.allowedDomains = builder.allowedDomains;
        this.maxDepth = builder.maxDepth;
        this.outputDir = builder.outputDir;
        this.enableDynamicFetcher = builder.enableDynamicFetcher;
        this.playwrightBrowserType = builder.playwrightBrowserType;
        this.playwrightHeadless = builder.playwrightHeadless;
        this.playwrightTimeout = builder.playwrightTimeout;
        this.deepDetectHtml = builder.deepDetectHtml;
        this.maxFileSize = builder.maxFileSize;
        this.userAgent = builder.userAgent;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public String getUserAgent() {
        return userAgent;
    }

    // Getters
    public int getMaxConnectionsPerDomain() {
        return maxConnectionsPerDomain;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public ParserConfig getParserConfig() {
        return parserConfig;
    }

    public DownloaderConfig getDownloaderConfig() {
        return downloaderConfig;
    }

    public StorageConfig getStorageConfig() {
        return storageConfig;
    }

    public String getStartUrl() {
        return startUrl;
    }

    public List<String> getAllowedDomains() {
        return allowedDomains;
    }

    public int getMaxDepth() {
        return maxDepth;
    }

    public String getOutputDir() {
        return outputDir;
    }

    public boolean isEnableDynamicFetcher() {
        return enableDynamicFetcher;
    }

    public String getPlaywrightBrowserType() {
        return playwrightBrowserType;
    }

    public boolean isPlaywrightHeadless() {
        return playwrightHeadless;
    }

    public int getPlaywrightTimeout() {
        return playwrightTimeout;
    }

    public boolean isDeepDetectHtmlUrl() {
        return this.deepDetectHtml;
    }

    public boolean isDeepDetectHtml() {
        return deepDetectHtml;
    }

    /**
     * 建造者模式，用于构建CrawlerConfig实例
     */
    public static class Builder {
        // 默认值
        private int maxConnectionsPerDomain = 5;
        private int connectionTimeout = 10000; // 10s
        private int readTimeout = 30000; // 30s
        private int maxRetries = 3;

        private ParserConfig parserConfig = new ParserConfig();
        private DownloaderConfig downloaderConfig = new DownloaderConfig();
        private StorageConfig storageConfig = new StorageConfig();

        // 运行时参数
        private String startUrl;
        private List<String> allowedDomains;
        private int maxDepth = 3;
        private String outputDir = "./output";
        private boolean enableDynamicFetcher = false; // Default to false
        private String playwrightBrowserType = "chromium";
        private boolean playwrightHeadless = true;
        private int playwrightTimeout = 30000; // 30s
        private boolean deepDetectHtml = true; // 是否深度检测HTML内容

        private long maxFileSize = 10 * 1024 * 1024; // 最大文件大小，10MB
        private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";

        public Builder setMaxConnectionsPerDomain(int maxConnectionsPerDomain) {
            this.maxConnectionsPerDomain = maxConnectionsPerDomain;
            return this;
        }

        public long getMaxFileSize() {
            return maxFileSize;
        }

        public Builder setMaxFileSize(long maxFileSize) {
            if (maxFileSize <= 0) {
                throw new IllegalArgumentException("最大文件大小必须大于0");
            }
            this.maxFileSize = maxFileSize;
            return this;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public Builder setUserAgent(String userAgent) {
            this.userAgent = userAgent;
            return this;
        }

        public Builder deepDetectHtml(boolean deepDetectHtml) {
            this.deepDetectHtml = deepDetectHtml;
            return this;
        }

        public Builder setConnectionTimeout(int connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
            return this;
        }

        public Builder setReadTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }

        public Builder setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public Builder setParserConfig(ParserConfig parserConfig) {
            this.parserConfig = parserConfig;
            return this;
        }

        public Builder setDownloaderConfig(DownloaderConfig downloaderConfig) {
            this.downloaderConfig = downloaderConfig;
            return this;
        }

        public Builder setStorageConfig(StorageConfig storageConfig) {
            this.storageConfig = storageConfig;
            return this;
        }

        public Builder setStartUrl(String startUrl) {
            this.startUrl = startUrl;
            return this;
        }

        public Builder setAllowedDomains(List<String> allowedDomains) {
            this.allowedDomains = allowedDomains;
            return this;
        }

        public Builder setMaxDepth(int maxDepth) {
            this.maxDepth = maxDepth;
            return this;
        }

        public Builder setOutputDir(String outputDir) {
            this.outputDir = outputDir;
            return this;
        }

        public Builder setEnableDynamicFetcher(boolean enableDynamicFetcher) {
            this.enableDynamicFetcher = enableDynamicFetcher;
            return this;
        }

        public Builder setPlaywrightBrowserType(String playwrightBrowserType) {
            if (playwrightBrowserType != null && !playwrightBrowserType.trim().isEmpty()) {
                this.playwrightBrowserType = playwrightBrowserType;
            }
            return this;
        }

        public Builder setPlaywrightHeadless(boolean playwrightHeadless) {
            this.playwrightHeadless = playwrightHeadless;
            return this;
        }

        public Builder setPlaywrightTimeout(int playwrightTimeout) {
            if (playwrightTimeout > 0) {
                this.playwrightTimeout = playwrightTimeout;
            }
            return this;
        }

        public CrawlerConfig build() {
            return new CrawlerConfig(this);
        }

        public boolean isDeepDetectHtml() {
            return deepDetectHtml;
        }

        public Builder setDeepDetectHtml(boolean deepDetectHtml) {
            this.deepDetectHtml = deepDetectHtml;
            return this;
        }
    }
}
