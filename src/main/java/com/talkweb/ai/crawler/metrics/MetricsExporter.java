package com.talkweb.ai.crawler.metrics;

import org.springframework.stereotype.Component;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.exporter.HTTPServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.net.InetSocketAddress;

/**
 * 指标导出器，负责收集和导出Prometheus指标以增强系统可观察性。
 */
@Component
public class MetricsExporter {
    private static final Logger logger = LoggerFactory.getLogger(MetricsExporter.class);
    // 定义指标
    private static final Counter pagesCrawled = Counter.build()
            .name("crawler_pages_crawled_total")
            .help("Total number of pages crawled")
            .register();
    
    private static final Counter pagesFailed = Counter.build()
            .name("crawler_pages_failed_total")
            .help("Total number of pages failed to crawl")
            .register();
    
    private static final Gauge activeThreads = Gauge.build()
            .name("crawler_active_threads")
            .help("Current number of active crawling threads")
            .register();
    
    private final HTTPServer server;
    
    /**
     * 构造函数，启动Prometheus HTTP服务器。
     * @param port HTTP服务器端口
     * @throws IOException 如果启动服务器时发生IO错误
     */
    public MetricsExporter(int port) throws IOException {
        server = new HTTPServer(new InetSocketAddress(port), io.prometheus.client.CollectorRegistry.defaultRegistry, true);
        logger.info("Prometheus metrics server started on port {}", port);
    }
    
    /**
     * 记录成功抓取的页面。
     */
    public void recordPageCrawled() {
        pagesCrawled.inc();
    }
    
    /**
     * 记录抓取失败的页面。
     */
    public void recordPageFailed() {
        pagesFailed.inc();
    }
    
    /**
     * 设置当前活跃线程数。
     * @param count 活跃线程数
     */
    public void setActiveThreads(int count) {
        activeThreads.set(count);
    }
    
    /**
     * 关闭Prometheus HTTP服务器。
     */
    public void shutdown() {
        if (server != null) {
            server.close();
            logger.info("Prometheus metrics server stopped");
        }
    }
}