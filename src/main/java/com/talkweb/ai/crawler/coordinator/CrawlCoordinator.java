package com.talkweb.ai.crawler.coordinator;

import org.springframework.stereotype.Component;
import com.talkweb.ai.crawler.config.CrawlerConfig;
import com.talkweb.ai.crawler.downloader.FileDownloader;
import com.talkweb.ai.crawler.fetcher.DynamicPageFetcher;
import com.talkweb.ai.crawler.fetcher.IFetcher;
import com.talkweb.ai.crawler.fetcher.PageFetcher;
import com.talkweb.ai.crawler.metrics.MetricsExporter;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.parser.ContentParser;
import com.talkweb.ai.crawler.reporter.CrawlReporter;
import com.talkweb.ai.crawler.storage.StorageManager;
import com.talkweb.ai.crawler.util.HttpUtils;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

/**
 * 爬取协调器，负责递归抓取逻辑和URL去重机制。
 */
@Component
public class CrawlCoordinator {
    private static final Logger logger = LoggerFactory.getLogger(CrawlCoordinator.class);

    private final int maxDepth;
    private final int maxExternalLinkDepth; // 外部链接的最大递归深度
    private final Set<String> visitedUrls;
    //小写的可访问域名列表
    private final List<String> allowedDomains;
    private final ExecutorService threadPool;
    private final IFetcher fetcher;
    private final ContentParser contentParser;
    private final StorageManager storageManager;
    private final Map<String, Semaphore> domainSemaphores;
    private final int maxConnectionsPerDomain;
    private final CrawlReporter reporter;
    private final MetricsExporter metricsExporter;

    private final FileDownloader fileDownloader;
    private final ICrawlCallback defaultCallback = new ICrawlCallback() {
        public void onPageCrawled(PageData pageData) {
            // 处理页面爬取完成后的回调逻辑
            if (pageData != null) {
                logger.debug("页面爬取成功: {}", pageData);
            } else {
                logger.warn("页面爬取失败或未返回有效数据");
            }
        }

        public void onError(String url, String message, Exception e) {
            // 处理爬取错误的回调逻辑
            if (e != null) {
                logger.error("爬取URL失败: {}, 错误: {},异常:{}", url, message, e.getMessage());
            } else {
                logger.warn("爬取URL失败: {}, 错误: {}", url, message);
            }
        }
    };
    /**
     * 是否深度检测HTML链接，默认为true。
     */
    private boolean deepDetectHtmlUrl = true;

    /**
     * 构造函数，初始化爬取协调器。
     * * @param maxExternalLinkDepth 最大外部链接递归深度
     *
     * @param maxExternalLinkDepth 最大递归深度
     * @param threads              线程数
     * @param metricsExporter      指标导出器
     */
    public CrawlCoordinator(CrawlerConfig config, int maxExternalLinkDepth, int threads, MetricsExporter metricsExporter) {
        this(config, maxExternalLinkDepth, threads,
            metricsExporter,
            config.isEnableDynamicFetcher() ? new DynamicPageFetcher(config) : new PageFetcher(config.getConnectionTimeout(), config.getReadTimeout(), config.getMaxRetries()),
            new ContentParser(config.getOutputDir()),
            new StorageManager(config.getOutputDir()),
            new FileDownloader(config.getOutputDir(), config.getMaxFileSize(), config.getConnectionTimeout(), config.getReadTimeout(), config.getMaxRetries(), config.getUserAgent()));
        this.deepDetectHtmlUrl = config.isDeepDetectHtml();
    }

    /**
     * 构造函数，用于测试，接受依赖注入。
     *
     * @param config               config
     * @param maxExternalLinkDepth 最大外部链接递归深度
     * @param threads              线程数
     * @param fetcher              fetcher
     * @param metricsExporter      指标导出器
     * @param contentParser        内容解析器
     * @param storageManager       存储管理器
     */
    public CrawlCoordinator(CrawlerConfig config, int maxExternalLinkDepth, int threads,
                            MetricsExporter metricsExporter,
                            IFetcher fetcher,
                            ContentParser contentParser, StorageManager storageManager) {
        this(config, maxExternalLinkDepth, threads, metricsExporter, fetcher, contentParser, storageManager,
            new FileDownloader(config.getOutputDir(), config.getMaxFileSize(), config.getConnectionTimeout(), config.getReadTimeout(), config.getMaxRetries(), config.getUserAgent()));
    }


    /**
     * 构造函数，用于测试，接受依赖注入。
     *
     * @param config               config
     * @param maxExternalLinkDepth 最大外部链接递归深度
     * @param threads              线程数
     * @param fetcher              fetcher
     * @param metricsExporter      指标导出器
     * @param contentParser        内容解析器
     * @param storageManager       存储管理器
     */
    public CrawlCoordinator(CrawlerConfig config, int maxExternalLinkDepth, int threads,
                            MetricsExporter metricsExporter,
                            IFetcher fetcher,
                            ContentParser contentParser, StorageManager storageManager, FileDownloader fileDownloader) {
        this.maxDepth = config.getMaxDepth();
        this.maxExternalLinkDepth = maxExternalLinkDepth;
        this.visitedUrls = new HashSet<>();
        this.allowedDomains = new ArrayList<>();

        this.threadPool = Executors.newFixedThreadPool(threads);
        this.fetcher = fetcher;
        this.contentParser = contentParser;
        this.fileDownloader = fileDownloader;
        this.storageManager = storageManager;
        this.maxConnectionsPerDomain = config.getMaxConnectionsPerDomain();
        this.domainSemaphores = new ConcurrentHashMap<>();
        this.reporter = new CrawlReporter(config.getOutputDir());
        this.metricsExporter = metricsExporter;

        this.setAllowedDomains(config.getAllowedDomains());
        logger.info("CrawlCoordinator initialized with thread pool size: {}", threads);
    }

    /**
     * 开始爬取指定URL。
     *
     * @param startUrl 起始URL
     */
    public void startCrawlAsync(String startUrl, ICrawlCallback callback) {
        if (isAllowedDomain(startUrl)) {
            logger.info("Starting crawl from: {}", startUrl);
            crawl(true, startUrl, maxDepth, callback);
        } else {
            logger.warn("起始URL不在允许的域名列表中: {}", startUrl);
        }
    }

    /**
     * 开始爬取指定URL。
     *
     * @param startUrl 起始URL
     */
    public PageData startCrawl(String startUrl) {
        if (isAllowedDomain(startUrl)) {
            logger.info("Starting crawl from: {}", startUrl);
            return crawl(false, startUrl, maxDepth, defaultCallback);
        } else {
            logger.warn("起始URL不在允许的域名列表中: {}", startUrl);
        }
        return null;
    }

    /**
     * 递归爬取URL。
     *
     * @param url   要爬取的URL
     * @param depth 当前深度
     */
    private PageData crawl(boolean async, String url, int depth, final ICrawlCallback callback) {

        PageData pageData = null;
        if (depth > maxDepth) {
            logger.info("达到最大深度限制，停止爬取: {}", url);
            callback.onError(url, "达到最大深度限制，停止爬取");
            return pageData;
        }

        if (visitedUrls.contains(url)) {
            logger.info("URL已访问，跳过: {}", url);
            return pageData;
        }
        if (!isAllowedDomain(url)) {
            reporter.log("INFO", "URL不在允许的域名列表中，跳过: " + url);
            callback.onError(url, "URL不在允许的域名列表中，跳过");
            return pageData;
        }
        synchronized (visitedUrls) {
            if (visitedUrls.contains(url)) {
                return pageData;
            }
            visitedUrls.add(url);
        }

        logger.info("爬取URL (深度 {}): {}", depth, url);
        boolean isHtml = UrlUtils.isHtmlLink(url);

        if (!isHtml && this.deepDetectHtmlUrl) {
            isHtml = HttpUtils.isHtmlUrl(url);
        }
        if (!isHtml) {
            //reporter.log("INFO", "非HTML链接，跳过: " + url);
            if (async) {
                threadPool.submit(() -> download(url, depth, callback));
            } else {
                pageData = download(url, depth, callback);
            }
        } else {
            if (async) {
                threadPool.submit(() -> fetchHtml(async, url, depth, callback));
            } else {
                // 同步调用
                pageData = fetchHtml(async, url, depth, callback);
            }
        }
        return pageData;
    }

    private PageData fetchHtml(boolean async, String url, int depth, final ICrawlCallback callback) {
        String domain = extractDomain(url);
        //Semaphore semaphore = getDomainSemaphore(domain);
        try {
          //  semaphore.acquire();
            if (!isAllowedDomain(url)) {
                reporter.log("INFO", "URL不在允许的域名列表中，跳过: " + url);
                callback.onError(url, "URL不在允许的域名列表中，跳过");
                return null;
            }
            // 抓取页面内容
            PageData pageData = fetcher.fetchPageContent(url);
            // 抓去成功后，降级depth
            depth--;
            // 解析页面数据
            pageData = contentParser.parse(pageData);

            if (pageData == null) {
                callback.onError(url, "解析页面内容失败");
                reporter.recordFailure(url, "Failed to parse page content.");
                if (metricsExporter != null) metricsExporter.recordPageFailed();
                return null;
            }

            {
                // 提取内部链接并递归爬取
                final List<String> internalLinks = pageData.getInternalLinks();
                //if (depth > 0)

                for (final String link : internalLinks) {
                    if (isAllowedDomain(link)) {
                        if (depth > 0) {
                            logger.info("递归爬取下一级内部链接: {},depth: {}", link, depth);
                            PageData next = crawl(async, link, depth, callback);
                            if (next != null) {
                                pageData.addPageData(link, next);
                            }
                        } else {
                            callback.onError(link, "已达到最大深度，跳过递归爬取");
                            reporter.log("INFO", "已达到最大深度，跳过递归爬取: " + link);
                        }
                    } else {
                        callback.onError(link, "忽略的外链，跳过");
                        reporter.log("INFO", "外链，跳过: " + link);
                    }
                }
            }
            {
                //外部链接递归爬取，但可以记录或处理
                List<String> externalLinks = pageData.getExternalLinks();
                if (maxExternalLinkDepth > 0) {
                    for (final String link : externalLinks) {
                        if (isAllowedDomain(link)) {
                            if (maxExternalLinkDepth > 0) {
                                logger.info("递归爬取外部链接: {},maxExternalLinkDepth: {}", link, maxExternalLinkDepth);
                                //外部链接层级自动下降？
                                PageData next = crawl(async, link, maxExternalLinkDepth, callback);
                                if (next != null) {
                                    callback.onPageCrawled(next);
                                    pageData.addPageData(link, next);
                                }
                            }
                        } else {
                            callback.onError(link, "忽略的外链，跳过");
                            reporter.log("INFO", "忽略的外链，跳过: " + link);
                        }
                    }
                } else {
                    for (final String link : externalLinks) {
                        if (isAllowedDomain(link)) {
                            callback.onError(link, "禁止访问外部链接，跳过");
                            reporter.log("INFO", "外部链接，跳过: " + link);
                        } else {
                            callback.onError(link, "外部链接，跳过");
                            reporter.log("INFO", "忽略的外链，跳过: " + link);
                        }
                    }
                }
            }
            // 保存页面数据
            storageManager.savePage(pageData);
            // 记录成功抓取
            reporter.recordSuccess(pageData);
            callback.onPageCrawled(pageData);
            logger.debug("页面爬取成功: {}", url);

            if (metricsExporter != null)
                metricsExporter.recordPageCrawled();

            return pageData;
        } catch (Exception e) {
            logger.error("抓取或解析页面时出错: {}", url, e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : "未知错误";
            callback.onError(url, errorMessage, e);
            reporter.recordFailure(url, errorMessage);
            if (metricsExporter != null) metricsExporter.recordPageFailed();
        } finally {
           // semaphore.release();
        }
        return null; // 异步调用不返回结果
    }

    /**
     * 下载文件。
     *
     * @param url          要下载的文件URL
     * @param ignoredDepth 忽略的深度参数（用于兼容性，实际不使用）
     */
    private PageData download(String url, int ignoredDepth, final ICrawlCallback callback) {
        try {
            final PageData meta = fileDownloader.download(url, true);
            if (meta != null && meta.getLocalPath() != null && !meta.getLocalPath().isEmpty()) {
                File f = new File(meta.getLocalPath());
                if (f.exists() && f.isFile() && f.length() > 0) {
                    // 如果文件存在且大小大于0，直接返回
                    long fs = f.lastModified();
                    if (fs > 0 && (System.currentTimeMillis() - fs < 60 * 60 * 1000)) {
                        // 如果文件存在且最近修改时间在1小时内，跳过下载
                        final PageData pageData = new PageData();
                        pageData.setUrl(url);
                        pageData.setLocalPath(meta.getLocalPath());
                        pageData.setLastModified(fs);
                        pageData.setProcessed(true); // 初始状态为未处理
                        reporter.recordSuccess(pageData);
                        if (metricsExporter != null) metricsExporter.recordPageCrawled();

                        callback.onPageCrawled(pageData);
                        return pageData;
                    }
                }
            }
            final PageData ai = fileDownloader.download(url, meta == null ? null : meta.getLocalPath(), false);
            if (ai == null) {
                reporter.recordFailure(url, "下载失败，附件信息为空");
                if (metricsExporter != null) metricsExporter.recordPageFailed();
                callback.onError(url, "下载失败");
                return ai;
            }
            final PageData pageData = new PageData();
            pageData.setUrl(url);
            String localPath = ai.getLocalPath();
            pageData.setLocalPath(localPath + ".meta");
            pageData.setLastModified(System.currentTimeMillis());
            pageData.setProcessed(false); // 初始状态为未处理
            pageData.setTitle(localPath); // 使用文件名作为标题
            pageData.setHtmlContent(""); // 下载的文件没有HTML内容
            pageData.setMarkdownContent(""); // 下载的文件没有Markdown内容
            pageData.addAttachment(ai); // 添加附件信息

            // 保存META数据
            storageManager.savePage(pageData);
            reporter.recordSuccess(pageData);
            callback.onPageCrawled(pageData);
            if (metricsExporter != null) metricsExporter.recordPageCrawled();

            return pageData;
        } catch (IOException e) {
            String errorMessage = e.getMessage() != null ? e.getMessage() : "未知错误";
            callback.onError(url, errorMessage, e);
            reporter.recordFailure(url, errorMessage);
            if (metricsExporter != null) metricsExporter.recordPageFailed();
        }
        return null;
    }

    /**
     * 设置允许抓取的域名列表。
     *
     * @param allowedDomains 允许抓取的域名列表
     */
    public void setAllowedDomains(final List<String> allowedDomains) {
        this.allowedDomains.clear();
        if (allowedDomains == null)
            return;
        allowedDomains.forEach(domain -> {
            if (domain == null || domain.trim().isEmpty()) return; // 跳过空或null的域名
            domain = domain.trim(); // 去除前后空格
            if (domain.startsWith("http://") || domain.startsWith("https://")) {
                domain = domain.replaceFirst("https?://", ""); // 去除协议部分
            }
            // 去除域名中的路径和查询参数部分
            domain = domain.split("/")[0]; // 只保留域名部分
            // 去除域名中的端口号
            domain = domain.split(":")[0]; // 只保留域名部分
            if (domain == null || domain.trim().isEmpty()) return;
            domain = domain.trim(); // 去除前后空格
            if (domain.startsWith("*.")) {
                // 如果域名以 "*." 开头，去掉前缀
                domain = domain.substring(2);
            }
            // 将域名转换为小写以确保一致性
            domain = domain.toLowerCase();
            // 添加到允许的域名列表中
            this.allowedDomains.add(domain);
        });
    }

    /**
     * 检查URL是否在允许的域名列表中。
     *
     * @param url 要检查的URL
     * @return 是否允许抓取
     */
    private boolean isAllowedDomain(String url) {
        if (allowedDomains.isEmpty()) {
            return true; // 如果没有指定允许的域名，则允许所有域名
        }

        try {
            // 将域名转换为小写以进行比较
            final String host = java.net.URI.create(url).toURL().getHost().toLowerCase();
            // 检查域名是否在允许的域名列表中
            return allowedDomains.contains(host) // 如果域名完全匹配
                || allowedDomains.stream().anyMatch(domain -> host.endsWith("." + domain)) // 如果域名是允许域名的子域名
                ;
        } catch (Exception e) {
            logger.error("解析URL时出错: {}", url, e);
        }
        return false;
    }

    /**
     * 关闭爬取协调器，释放资源。
     */
    public void shutdown() {
        threadPool.shutdown();
        try {
            if (!threadPool.awaitTermination(60, TimeUnit.SECONDS)) {
                threadPool.shutdownNow();
            }
        } catch (InterruptedException e) {
            threadPool.shutdownNow();
            Thread.currentThread().interrupt();
        }
        domainSemaphores.clear();

        if (this.fetcher != null) {
            try {
                this.fetcher.close();
                logger.info("Fetcher closed successfully.");
            } catch (Exception e) { // AutoCloseable.close() throws Exception
                logger.error("Error closing fetcher: {}", e.getMessage(), e);
            }
        }

        try {
            reporter.generateReport();
            logger.info("Crawl completed and report generated.");
        } catch (IOException e) {
            logger.error("生成抓取报告时出错: {}", e.getMessage());
        }
    }

    public void awaitTermination() {
        threadPool.shutdown();
        try {
            if (!threadPool.awaitTermination(60 * 1000, TimeUnit.NANOSECONDS)) {
                threadPool.shutdownNow();
            }
        } catch (InterruptedException e) {
            threadPool.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 提取URL的域名。
     *
     * @param url URL字符串
     * @return 域名字符串
     */
    private String extractDomain(String url) {
        try {
            return java.net.URI.create(url).toURL().getHost();
        } catch (Exception e) {
            logger.error("提取域名时出错: {}", url, e);
            return "unknown";
        }
    }

    /**
     * 获取指定域名的信号量。
     *
     * @param domain 域名
     * @return 信号量对象
     */
    private Semaphore getDomainSemaphore(String domain) {
        return domainSemaphores.computeIfAbsent(domain, a -> new Semaphore(maxConnectionsPerDomain));
    }

    /**
     * Checks if the thread pool is effectively idle, meaning no active tasks and an empty queue.
     * This can be used to determine if all crawling work has been completed.
     *
     * @return true if the thread pool is idle, false otherwise.
     */
    public boolean isEffectivelyIdle() {
        if (threadPool instanceof ThreadPoolExecutor executor) {
            return executor.getActiveCount() == 0 && executor.getQueue().isEmpty();
        }
        // If not a ThreadPoolExecutor, or if pool is null, consider it idle or handle as an error.
        // For robustness, if it's not the expected type, it might be safer to assume not idle or log a warning.
        // However, given it's initialized as Executors.newFixedThreadPool, it will be a ThreadPoolExecutor.
        return threadPool == null || threadPool.isShutdown(); // Fallback for other ExecutorService types or if null
    }
}
