package com.talkweb.ai.crawler.coordinator;

import com.talkweb.ai.crawler.model.PageData;

/**
 * 爬取回调接口，用于处理爬取结果和错误。
 */
public interface ICrawlCallback {
    default public  void onPageCrawled(PageData pageData) {
    }

    default public void onError(String url, String message) {
        onError(url, message, null);
    }
    default public void onError(String url,String message, Exception e) {
    }

}
