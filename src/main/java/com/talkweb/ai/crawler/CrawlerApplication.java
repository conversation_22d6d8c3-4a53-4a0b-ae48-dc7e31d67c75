package com.talkweb.ai.crawler;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Main entry point for the Crawler Service in server mode.
 */
@OpenAPIDefinition(
    info = @Info(
        title = "Crawler Service API",
        version = "1.0",
        description = "API for managing and monitoring web crawl tasks.",
        contact = @Contact(
            name = "Support Team",
            url = "https://example.com/support",
            email = "<EMAIL>"
        ),
        license = @License(
            name = "Apache 2.0",
            url = "https://www.apache.org/licenses/LICENSE-2.0.html"
        )
    ),
    servers = {
        @Server(url = "http://localhost:8080", description = "Local Development"),
        @Server(url = "https://api.example.com", description = "Production")
    }
)
@SpringBootApplication
@EnableAsync
public class CrawlerApplication {

    /**
     * Main method to start the Spring Boot application.
     *
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(CrawlerApplication.class, args);
    }
}
