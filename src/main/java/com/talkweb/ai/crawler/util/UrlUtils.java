package com.talkweb.ai.crawler.util;

import org.slf4j.Logger;

import java.io.File;
import java.io.IOException;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.slf4j.LoggerFactory.getLogger;

public class UrlUtils {

    public static final Pattern fileNamePattern = Pattern.compile("filename\\*=([^']+)'[^']*'([^;]+)");
    public static final Pattern fileNamePattern2 = Pattern.compile("filename=\"?([^\";]+)\"?");
    public static final Logger logger = getLogger(UrlUtils.class);

    /**
     * 从URL中提取域名(包含端口)
     */
    public static String extractDomain(String url) {
        try {
            URL parsedUrl = newURL(url);
            int port = parsedUrl.getPort();
            return parsedUrl.getHost() + (isDefaultPort(parsedUrl.getProtocol(), port) ? "" : ":" + port);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 解析相对URL为完整URL，支持路径规范化
     * 示例：
     * base: http://example.com/a/b/
     * rel:  ../c.html → http://example.com/a/c.html
     *
     * @param baseUrl     基准URL
     * @param relativeUrl 相对路径或绝对URL
     * @return 合并后的完整URL
     */
    public static String resolveRelativeUrl(String baseUrl, String relativeUrl) {
        try {
            URI base = new URI(baseUrl);
            URI rel = new URI(relativeUrl);
            URI resolved = base.resolve(rel).normalize();
            return resolved.toString();
        } catch (URISyntaxException e) {
            // fallback: 简单拼接
            return baseUrl + (relativeUrl.startsWith("/") ? "" : "/") + relativeUrl;
        }
    }

    /**
     * 规范化文件名
     */
    public static String sanitizeFilename(String url) {
        try {
            String decoded = URLDecoder.decode(url, StandardCharsets.UTF_8);
            return decoded.replaceAll("[^\\w\\d.-]", "_");
        } catch (Exception e) {
            return url.replaceAll("[^\\w\\d.-]", "_");
        }
    }

    /**
     * 判断是否是有效的 HTTP 链接
     */
    public static boolean isHttpLink(String href) {
        return href != null &&
            !href.isBlank() &&
            !href.startsWith("#") &&
            !href.startsWith("javascript:") &&
            !href.startsWith("mailto:") &&
            !href.startsWith("tel:");
    }

    /**
     * 判断是否是有效的 HTTP/html页面 链接
     */
    public static boolean isHtmlLink(String href) {
        if (!isHttpLink(href)) {
            return false;
        }
        String lowerHref = href.toLowerCase();
        // 检查是否以常见的 HTML 页面扩展名结尾
        boolean isHtmlPage = lowerHref.endsWith(".html") || lowerHref.endsWith(".htm")
            || lowerHref.endsWith(".shtml") || lowerHref.endsWith(".php")
            || lowerHref.endsWith(".asp") || lowerHref.endsWith(".jsp");
        if (isHtmlPage) return true;

        // 检查是否有路径部分
        try {
            URL url = newURL(href);
            String path = url.getPath();

            if (path == null || path.isEmpty() || path.equals("/")) {
                // 如果根目录
                return true; // 如果没有路径，直接返回 false
            }
        } catch (MalformedURLException e) {
            logger.warn("Malformed URL: {}", href, e);
        }
        return false; // 如果 URL 无效，返回 false
    }

    /**
     * 去除锚点（#fragment）、可选去除 query 参数
     */
    public static String normalizeUrl(String url) {
        try {
            URI uri = new URI(url).normalize();
            // 可选：去除 query
          // uri = new URI(uri.getScheme(), uri.getAuthority(), uri.getPath(), uri.getQuery(), uri.getFragment());
            return uri.toString();
        } catch (URISyntaxException e) {
            logger.warn("Failed to normalize URL: {}", url, e);
            return null; // 返回 null 表示无法规范化
        }
    }

    public static boolean isDefaultPort(final String protocol, int port) {
        return port == -1 || port == getDefaultPort(protocol);
    }

    public static int getDefaultPort(String scheme) {
        return switch (scheme.toLowerCase()) {
            case "http" -> 80;
            case "https" -> 443;
            case "ftp" -> 21;
            default -> -1;
        };
    }

    /**
     * 判断是否可以下载的附件
     *
     * @param url               URL
     * @param downloadableTypes 可下载的附件类型（需包含点，如：.pdf, .doc）
     * @return 是否为可下载附件
     */
    public static boolean isDownloadable(String url, final Set<String> downloadableTypes) {
        String fileExtension = getFileExtension(url);
        return !fileExtension.isEmpty() && downloadableTypes.contains(fileExtension.toLowerCase());
    }

    /**
     * 获取 URL 的文件扩展名（包含点，不含 query/fragment）
     * 如 http://example.com/image.jpg?token=abc 返回 .jpg
     *
     * @param url URL字符串
     * @return 文件扩展名（包含点），如 .jpg；无扩展名则返回空串
     */
    public static String getFileExtension(String url) {
        final String fileName = getFileName(url);
        if (fileName.isEmpty()) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex >= 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex).toLowerCase(); // 含点
        }
        return "";
    }

    /**
     * 从 URL 或 HTTP 响应头中提取文件名
     *
     * @param fileUrl    文件 URL
     * @param connection HTTPURLConnection 实例
     * @return 文件名（默认 fallback 为 downloaded_file_<timestamp>）
     */
    public static String extractFileName(String fileUrl, HttpURLConnection connection) {
        return extractFileName(fileUrl, connection, null);
    }

    /**
     * 获取 URL 的文件名（不含 query、fragment）
     * 如 http://example.com/image.jpg?token=abc 返回 image.jpg
     *
     * @param url URL字符串
     * @return 文件名
     */
    public static String getFileName(String url) {
        return extractFileName(url, null);
    }

    /**
     * 从 URL 或 HTTP 响应头中提取文件名
     *
     * @param fileUrl    文件 URL
     * @param connection HTTPURLConnection 实例
     * @return 文件名（默认 fallback 为 downloaded_file_<timestamp>）
     */
    public static String extractFileName(String fileUrl, HttpURLConnection connection, final String fallbackFileName) {
        // 1. 从 Content-Disposition 中提取文件名
        if (connection != null) {
            String contentDisposition = connection.getHeaderField("Content-Disposition");
            if (contentDisposition != null) {
                String fileName = null;

                // 支持标准 filename= 和 filename*= 编码格式
                Matcher matcher = fileNamePattern.matcher(contentDisposition);
                if (matcher.find()) {
                    // filename*=UTF-8''encoded.txt
                    try {
                        String encoding = matcher.group(1) != null ? matcher.group(1) : "UTF-8";
                        fileName = URLDecoder.decode(matcher.group(2), encoding);
                    } catch (Exception ignored) {
                    }
                } else {
                    matcher = fileNamePattern2.matcher(contentDisposition);
                    if (matcher.find()) {
                        fileName = matcher.group(1);
                    }
                }

                if (fileName != null && !fileName.isEmpty()) {
                    // 去除非法字符
                    return sanitizeFileName(fileName);
                }
            }
        }

        // 2. 从 URL path 中提取
        try {
            String fallbackFileName1 = getFileNameFromUrl(fileUrl, fallbackFileName);
            if (fallbackFileName1 != null) return fallbackFileName1;
        } catch (MalformedURLException ignored) {
        }
        // 3. fallback：默认文件名
        return fallbackFileName;
    }

    public static String getFileNameFromUrl(String fileUrl) throws MalformedURLException {
        return getFileNameFromUrl(fileUrl, null);
    }

    public static String getFileNameFromUrl(String fileUrl, String fallbackFileName) throws MalformedURLException {
        URL url = newURL(fileUrl);
        String path = URLDecoder.decode(url.getPath());

        if (path == null || path.isEmpty() || path.equals("/")) {
            // 如果路径为空或仅为根目录，使用默认文件名
            // 如果路径是目录或没有文件名部分，使用 fallbackFileName
            // 如果没有指定 fallbackFileName，则使用默认的 "index.html"
            return fallbackFileName == null ? "index.html" : fallbackFileName;
        }
        // 去除 query 和 fragment,存在完整的文件名
        String name = new File(path).getName();
        if (!name.isEmpty()) {
            return sanitizeFileName(name);
        }
        return fallbackFileName == null ? "index.html" : fallbackFileName;
    }

    /**
     * 清理并规范化文件名：
     * - 替换非法字符为 '_'
     * - 限制总长度（默认 255）
     * - 保留扩展名（如 .pdf）
     *
     * @param fileName 原始文件名
     * @return 安全的文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unnamed_file";
        }

        // 1. 去除路径穿越字符 / \ ..
        fileName = fileName.replaceAll("[/\\\\]+", "_");

        // 2. 移除控制字符（不可打印）
        fileName = fileName.replaceAll("[\\p{Cntrl}]", "");

        // 3. 替换非法字符（仅保留中英文、数字、. _ -）
        fileName = fileName.replaceAll("[^a-zA-Z0-9._\\-\\u4e00-\\u9fa5]", "_");

        // 4. 合并多个下划线为一个
        fileName = fileName.replaceAll("_+", "_");

        // 5. 去除前后下划线
        fileName = fileName.replaceAll("^_+|_+$", "");

        if (fileName.isEmpty()) {
            return "unnamed_file";
        }

        // 6. 保留扩展名，限制主体长度
        int maxLength = 255; // 文件系统限制，通常为 255
        int extIndex = fileName.lastIndexOf('.');
        String namePart;
        String extPart;

        if (extIndex > 0 && extIndex < fileName.length() - 1) {
            namePart = fileName.substring(0, extIndex);
            extPart = fileName.substring(extIndex); // 含点
        } else {
            namePart = fileName;
            extPart = "";
        }

        int allowedNameLength = maxLength - extPart.length();
        if (namePart.length() > allowedNameLength) {
            namePart = namePart.substring(0, allowedNameLength);
        }

        return namePart + extPart;
    }

    /**
     * 根据 URL 和文件名生成本地保存路径
     *
     * @param url 原始文件的 URL
     * @return 本地完整路径
     * @throws IOException 创建目录失败时抛出
     */
    public static String generateLocalPath(String outputDir, String url) throws IOException {
        return generateLocalPath(outputDir, url, null);
    }

    /**
     * 根据 URL 和文件名生成本地保存路径
     *
     * @param url      原始文件的 URL
     * @param fileName 文件名（已清理）
     * @return 本地完整路径
     * @throws IOException 创建目录失败时抛出
     */
    public static String generateLocalPath(String outputDir, String url, String fileName) throws IOException {
        String normalizedUrlStr = UrlUtils.normalizeUrl(url);
        if (normalizedUrlStr == null) {
            logger.warn("URL normalization failed for: {}. Attempting to use original URL for path generation.", url);
            normalizedUrlStr = url;
        }

        URL u;
        try {
            u = newURL(normalizedUrlStr);
        } catch (MalformedURLException e) {
            logger.error("Malformed URL after normalization/fallback: {}. Cannot generate path.", normalizedUrlStr, e);
            throw new IOException("Malformed URL for path generation: " + normalizedUrlStr, e);
        }
        String host = u.getHost();

        // 拼接端口（如果不是默认端口）
        if (!UrlUtils.isDefaultPort(u.getProtocol(), u.getPort())) {
            host += ":" + u.getPort();
        }

        String path = u.getPath();
        if (path == null) path = "";

        // 如果路径是文件（以扩展名结尾），去掉最后一段
        if (path.matches(".*/[^/]+\\.[a-zA-Z0-9]+$")) {
            path = path.substring(0, path.lastIndexOf('/'));
        }

        // 去掉前导 /
        if (path.startsWith("/")) {
            path = path.substring(1);
        }

        // 构建本地目录路径（无 attachments）
        Path dirPath = Paths.get(outputDir, host, path);
        //不自动创建目录
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
        }
        // fileName is assumed to be already sanitized by UrlUtils.extractFileName
        // 返回完整文件路径
        if (fileName == null) {
            fileName = getFileName(url);
        }
        return dirPath.resolve(fileName).toString();
    }

    public static String getDownloadedFileName(String url) throws MalformedURLException {
        String fileName = getFileNameFromUrl(url);
        String suffix = getFileExtension(url);
//
//        if (suffix.endsWith(".html")
//            || suffix.endsWith(".htm")
//            || suffix.endsWith(".HTML")
//            || suffix.endsWith(".HTM")
//            || suffix.endsWith(".shtml")
//            || suffix.endsWith(".SHTML")
//        ) {
//            // 如果是 HTML 文件，替换扩展名为 .md
//            fileName = fileName.replaceAll(suffix, ".md");
//        }
        return fileName;
    }

    public static URL newURL(String url) throws MalformedURLException {
        if (url == null || url.isBlank()) {
            throw new MalformedURLException("URL cannot be null or empty");
        }
        // 处理可能的相对路径
//        if (!url.startsWith("http://") && !url.startsWith("https://")) {
//            url = "http://" + url; // 默认使用 http 协议
//        }
        return URI.create(url).toURL();
    }
}
