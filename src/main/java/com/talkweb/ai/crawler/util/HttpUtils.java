package com.talkweb.ai.crawler.util;

import org.slf4j.Logger;

import java.io.IOException;
import java.net.*;

import static org.slf4j.LoggerFactory.getLogger;

public class HttpUtils {

    public static final Logger logger = getLogger(HttpUtils.class);
    /**
     * 默认的连接和读取超时时间，单位为毫秒。
     * 这里设置为5秒，可以根据实际需求调整。
     */
    private static final int DEFAULT_TIMEOUT_MS = 5000; // 5秒

    /**
     * Checks if the given URL likely returns HTML content by sending a HEAD request
     * and checking the Content-Type header.
     *
     * @param urlString The URL to check.
     * @return true if the Content-Type indicates HTML, false otherwise or if an error occurs.
     */
    public static boolean isHtmlUrl(String urlString) {
        if (urlString == null || urlString.isEmpty()) {
            return false;
        }

        HttpURLConnection connection = null;
        try {
            URL url = URI.create(urlString).toURL();
            connection = (HttpURLConnection) url.openConnection();

            // Set request method to HEAD
            connection.setRequestMethod("HEAD");

            // Allow auto-redirects (HttpURLConnection handles 3xx redirects by default for GET,
            // but for HEAD, it might depend on the Java version and server behavior.
            // Explicitly setting followRedirects can be beneficial, though it's true by default.)
            connection.setInstanceFollowRedirects(true);


            // Set timeouts
            connection.setConnectTimeout(DEFAULT_TIMEOUT_MS);
            connection.setReadTimeout(DEFAULT_TIMEOUT_MS);

            // Disable caching to ensure fresh response
            connection.setUseCaches(false);

            // Set a common User-Agent, some servers might block requests without it
            // This is a good practice to avoid being blocked by servers that require a User-Agent header.
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 ");


            int responseCode = connection.getResponseCode();

            // We are interested in successful responses (2xx)
            // For HEAD requests, redirects (3xx) should have been followed if setInstanceFollowRedirects(true) works as expected.
            // If redirects are not automatically followed for HEAD, you might get a 3xx here.
            // For simplicity, we'll primarily check 200 OK.
            if (responseCode == HttpURLConnection.HTTP_OK) {
                String contentType = connection.getContentType();
                if (contentType != null) {
                    return contentType.toLowerCase().contains("text/html");
                } else {
                    // Content-Type header is missing, less certain but could still be HTML.
                    // Depending on strictness, you might return false or try other heuristics.
                    logger.warn("Content-Type header missing for URL: {}", urlString);
                    return false;
                }
            } else if (responseCode >= HttpURLConnection.HTTP_MOVED_PERM && responseCode <= HttpURLConnection.HTTP_SEE_OTHER) {
                // Handle redirects if not automatically followed.
                // For a robust solution, you might want to recursively call isHtmlUrl with the new location.
                String newUrl = connection.getHeaderField("Location");
                logger.info("URL {} redirected to {} with code {}", urlString, newUrl, responseCode);
                if (newUrl != null && !newUrl.isEmpty()) {
                    // Basic redirect handling (one hop)
                    // For multiple hops, a loop or recursion would be needed.
                    // Be careful about redirect loops.
                    // return isHtmlUrl(newUrl); // Recursive call - ensure max redirect depth
                }
                return false; // Or attempt to follow redirect
            } else {
                logger.warn("HEAD request to {} failed with response code: {}", urlString, responseCode);
                return false;
            }

        } catch (MalformedURLException e) {
            logger.error("Invalid URL format: " + urlString, e);
            return false;
        } catch (ProtocolException e) {
            logger.error("Protocol error (e.g., trying to set HEAD on a non-HTTP URL) for: " + urlString, e);
            return false;
        } catch (IOException e) {
            // This can include connection timeouts, host not found, etc.
            logger.warn("IOException during HEAD request to " + urlString + ": " + e.getMessage(), e);
            return false;
        } catch (SecurityException e) {
            logger.warn("Security exception for URL: " + urlString + ". Check network permissions.", e);
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    public static void main(String[] args) {
        String[] testUrls = {
            "https://www.google.com",
            "https://www.example.com",
            "https://www.w3.org/TR/PNG/iso_8859-1.txt", // Should be text/plain
            "https://upload.wikimedia.org/wikipedia/commons/7/70/Example.png", // Should be image/png
            "http://httpstat.us/301", // Redirect
            "http://httpstat.us/302", // Redirect
            "https://nonexistent-domain-for-testing.com", // Should fail
            "https://www.oracle.com/java/technologies/javase-jdk21-downloads.html" // Real world example
        };

        for (String url : testUrls) {
            System.out.println("Checking URL: " + url);
            boolean isHtml = isHtmlUrl(url);
            System.out.println("Is HTML? " + isHtml);
            System.out.println("------------------------------------");
        }
    }
}
