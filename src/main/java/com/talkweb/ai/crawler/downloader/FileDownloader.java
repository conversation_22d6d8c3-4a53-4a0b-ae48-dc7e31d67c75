package com.talkweb.ai.crawler.downloader;

import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

/**
 * 文件下载器，负责下载附件文件（如word、txt、pdf）。
 */
public class FileDownloader {
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 最大文件大小，10MB
    public static final int CONNECT_TIMEOUT = 5000; // 连接超时时间，单位：毫秒
    public static final int READ_TIMEOUT = 10000;
    public static final int MAX_RETRIES = 1; // 最大重试次数
    private static final Logger logger = LoggerFactory.getLogger(FileDownloader.class);
    // 读取超时时间，单位：毫秒
    private final String outputDir;
    private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    private long maxFileSize; // 最大文件大小，10MB
    private int connectTimeout = CONNECT_TIMEOUT; // 连接超时时间
    private int readTimeout = READ_TIMEOUT; // 读取超时时间
    private int maxRetries = 1; // 最大重试次数

    /**
     * 构造函数，初始化文件下载器。
     *
     * @param outputDir 输出目录
     */
    public FileDownloader(String outputDir) {
        this(outputDir, MAX_FILE_SIZE);
    }

    public FileDownloader(String outputDir, long maxFileSize) {
        this(outputDir, maxFileSize, CONNECT_TIMEOUT, READ_TIMEOUT,
            MAX_RETRIES,
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    }

    public FileDownloader(String outputDir, long maxFileSize, int connectTimeout, int readTimeout, int maxRetries, String userAgent) {
        this.outputDir = outputDir;
        this.maxFileSize = maxFileSize;
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.userAgent = userAgent;
        this.maxRetries = maxRetries;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        if (maxFileSize <= 0) {
            throw new IllegalArgumentException("最大文件大小必须大于0");
        }
        this.maxFileSize = maxFileSize;
    }

    /**
     * 下载文件并保存到指定目录。
     *
     * @param fileUrl              文件URL
     * @param parseAttachmentsOnly 是否仅解析附件，不实际下载
     * @return 附件信息对象，如果下载失败则返回null
     */
    public PageData download(String fileUrl, boolean parseAttachmentsOnly) {
        return download(fileUrl, null, parseAttachmentsOnly);
    }

    public PageData download(String fileUrl) {
        return download(fileUrl, null, false);
    }

    public PageData download(String fileUrl, String target) {
        return download(fileUrl, target, false);
    }

    /**
     * 下载文件并保存到指定目录。
     *
     * @param fileUrl              文件URL
     * @param parseAttachmentsOnly 是否仅解析附件，不实际下载
     * @return 附件信息对象，如果下载失败则返回null
     */
    public PageData download(String fileUrl, String targetFile, boolean parseAttachmentsOnly) {
        if (parseAttachmentsOnly) {
            logger.info("仅解析附件，不实际下载: {}", fileUrl);

            if (fileUrl == null || fileUrl.isEmpty()) {
                throw new IllegalArgumentException("URL不能为空");
            }
            if (targetFile == null || targetFile.isEmpty()) {
                String fileName = UrlUtils.getFileName(fileUrl);
                try {
                    targetFile = UrlUtils.generateLocalPath(outputDir, fileUrl, fileName);
                } catch (IOException e) {
                    throw new IllegalArgumentException("生成本地路径失败: " + fileUrl, e);
                }
            }
            return new PageData(fileUrl, targetFile);
        }
        return download(fileUrl, targetFile, fileUrl);
    }

    /**
     * 下载文件并保存到指定目录。
     *
     * @param fileUrl     文件URL
     * @param description 文件描述
     * @return 附件信息对象，如果下载失败则返回null
     */
    public PageData download(String fileUrl, String target, String description) {
        int retries = 0;

        if (fileUrl == null || fileUrl.isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }
        if (target == null || target.isEmpty()) {
            String fileName = UrlUtils.getFileName(fileUrl);
            try {
                target = UrlUtils.generateLocalPath(outputDir, fileUrl, fileName);
            } catch (IOException e) {
                throw new IllegalArgumentException("生成本地路径失败: " + fileUrl, e);
            }
        }
        try {
            URL url = UrlUtils.newURL(fileUrl);
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("无效URL: " + fileUrl, e);
        }
        while (retries < maxRetries) {
            try {
                PageData result = downloadInternal(fileUrl, target, description);
                if (result != null) {
                    return result;
                }
            } catch (MalformedURLException e) {
                throw new IllegalArgumentException("无效URL: " + fileUrl, e);
            } catch (IllegalArgumentException ex) {
                throw ex; // 如果是非法参数异常，直接抛出
            }
            logger.debug("下载:{}失败，重试剩余次数: {}", fileUrl, retries);
            retries++;
            if (retries < maxRetries) {
                // 在重试前等待一段时间，采用指数退避策略
                try {
                    TimeUnit.MILLISECONDS.sleep((long) Math.pow(2, retries) * 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return null; // 如果线程被中断，直接返回null
                }
            }
        }
        return null; // 如果所有重试都失败，返回null
    }

    /**
     * 下载文件并保存到指定目录。
     *
     * @param fileUrl     文件URL
     * @param description 文件描述
     * @return 附件信息对象，如果下载失败则返回null
     */
    protected PageData downloadInternal(String fileUrl, String target, String description) throws MalformedURLException {
        long totalBytesRead = 0;
        long fileSize = 0;
        String localPath = target;
        try {
            final URL url = UrlUtils.newURL(fileUrl);
            final HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置连接和读取超时
            connection.setConnectTimeout(connectTimeout);
            connection.setReadTimeout(readTimeout);
            // 设置请求头

            connection.setRequestProperty("User-Agent", userAgent);
            connection.setInstanceFollowRedirects(true);
            HttpURLConnection.setFollowRedirects(true);
            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                connection.disconnect();
                logger.error("下载文件失败，响应码: {} URL: {}", responseCode, fileUrl);
                return null;
            }

            // 检查文件大小
            fileSize = connection.getContentLengthLong();
            if (fileSize > maxFileSize) {
                connection.disconnect();
                logger.error("文件过大，超过最大限制: {} bytes,实际:{} bytes, URL: {}", maxFileSize, fileSize, fileUrl);
                return null;
            }
            Path dirPath = Paths.get(target).getParent();
            if (dirPath != null && !Files.exists(dirPath)) {
                try {
                    Files.createDirectories(dirPath);
                } catch (IOException e) {
                    logger.error("创建目录时出错，路径: {}, 错误: {}", dirPath, e.getMessage());
                    return null;
                }
            }

            // 下载文件
            File file = new File(localPath);
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(file)) {
                byte[] buffer = new byte[4096];
                int bytesRead;


                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;

                    if (totalBytesRead > maxFileSize) {
                        connection.disconnect();
                        new File(localPath).delete();
                        logger.error("文件过大，超过最大限制: {} bytes,实际:{} bytes, URL: {}", maxFileSize, totalBytesRead, fileUrl);
                        return null;
                    }
                }
            } catch (IOException e) {
                logger.error("下载文件内容时出错: {}, 错误: {}", fileUrl, e.getMessage());
                new File(localPath).delete();
                connection.disconnect();
                return null;
            }
            connection.disconnect();
            logger.info("文件下载成功: {} 保存到: {},{} bytes,contentLength: {},writed: {},limited: {}", fileUrl, localPath, new File(localPath).length(), fileSize, totalBytesRead, maxFileSize);
            final PageData ai = new PageData(fileUrl, localPath);
            ai.setContentLength(fileSize);
            ai.setLastModified(connection.getLastModified());
            if (ai.getLastModified() > 0) {
                // 设置文件的最后修改时间
                file.setLastModified(ai.getLastModified());
            }
            ai.setDescription(description);
            ai.setContentType(connection.getContentType());
            return ai;
        } catch (IllegalArgumentException | MalformedURLException e) {
            logger.error("下载文件时出错: {}, 错误: {}", fileUrl, e.getMessage());
            throw e;
        } catch (IOException e) {
            logger.error("下载文件时出错: {}, 错误: {}", fileUrl, e.getMessage());
            return null;
        }
    }


}
