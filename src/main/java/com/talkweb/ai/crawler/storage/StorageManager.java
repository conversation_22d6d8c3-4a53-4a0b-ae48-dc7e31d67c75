package com.talkweb.ai.crawler.storage;

import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 存储管理器，负责将页面数据和附件保存到文件系统。
 */
public class StorageManager {
    private static final Logger logger = LoggerFactory.getLogger(StorageManager.class);
    private final String rootDir;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");

    /**
     * 构造函数，初始化存储管理器。
     *
     * @param rootDir 根目录路径
     */
    public StorageManager(String rootDir) {
        this.rootDir = rootDir;
    }

    /**
     * 加载页面数据。
     *
     * @param url
     * @return
     */
    public PageData loadPageData(String url) {

        try {
            Path filePath = Path.of(UrlUtils.generateLocalPath(rootDir, url));
            if (Files.exists(filePath)) {
                try {
                    String content = Files.readString(filePath);
                    PageData pageData = new PageData();
                    pageData.setUrl(url);
                    pageData.setLocalPath(filePath.toString());
                    pageData.setMarkdownContent(content);
                    pageData.setLastModified(filePath.toFile().lastModified());
                    // 这里可以添加更多的解析逻辑来填充其他字段
                    return pageData;
                } catch (IOException e) {
                    logger.error("加载页面数据失败，URL: {}, 错误: {}", url, e.getMessage());
                }
            } else {
                logger.warn("页面数据文件不存在，URL: {}", url);
            }
        } catch (Exception e) {
            logger.error("加载页面数据时发生异常，URL: {}, 错误: {}", url, e.getMessage());
        }
        return null;
    }

    /**
     * 保存页面数据到文件系统。
     *
     * @param pageData 页面数据对象
     * @return 保存的文件路径
     * @throws IOException 如果保存过程中发生IO错误
     */
    public Path savePage(PageData pageData) throws IOException {
        // 生成保存路径
        String localPath = pageData.getLocalPath();

        if (localPath == null || localPath.isEmpty()) {
            localPath = UrlUtils.generateLocalPath(rootDir, pageData.getUrl(), null);
        }

        Path filePath = Path.of(localPath);

        // 创建目录
        Files.createDirectories(filePath.getParent());

        if (pageData.getHtmlContent() != null) {
            // 如果HTML内容不为空，先保存HTML内容
            // 确保文件名以.html结尾
            String htmlFileName = filePath.toString();
            if (!htmlFileName.endsWith(".html")) {
                htmlFileName += ".html";
            }
            Path path = Path.of(htmlFileName);
            if (Files.exists(path)) {
                logger.warn("文件已存在，覆盖: {}", htmlFileName);
            } else {
                logger.info("创建新文件: {}", htmlFileName);
            }
            Files.writeString(path, pageData.getHtmlContent());
            logger.info("HTML内容已保存到: {}", filePath);
        }
        if (pageData.getMarkdownContent() != null) {
            // 如果Markdown内容不为空，保存为.md文件
            String markdownFileName = filePath.toString();
            if (!markdownFileName.endsWith(".md")) {
                markdownFileName += ".md";
            }
            Path path = Path.of(markdownFileName);
            if (Files.exists(path)) {
                logger.warn("文件已存在，覆盖: {}", markdownFileName);
            } else {
                logger.info("创建新文件: {}", markdownFileName);
            }
            Files.writeString(path, pageData.getMarkdownContent());
        }
        // 写入Markdown内容
        String metaFileName = filePath + ".meta";
        Path path = Path.of(metaFileName);
        if (Files.exists(path)) {
            logger.warn("文件已存在，覆盖: {}", metaFileName);
        } else {
            logger.info("创建新文件: {}", metaFileName);
        }
        try (FileWriter writer = new FileWriter(path.toFile())) {
            // 写入元数据
            writer.write("---\n");
            writer.write("url: " + pageData.getUrl() + "\n");
            if (pageData.getTitle() != null) {
                writer.write("title: " + pageData.getTitle() + "\n");
            } else {
                writer.write("title: 无标题\n");
            }
            writer.write("fetchTimestamp: " + pageData.getFetchTimestamp().toString() + "\n");
            writer.write("lastModified: " + pageData.getLastModified() + "\n");
            writer.write("responseCode: " + (pageData.getResponseCode()) + "\n");
            writer.write("contentLength: " + (pageData.getContentLength()) + "\n");
            writer.write("localPath: " + pageData.getLocalPath() + "\n");
            writer.write("contentType: " + pageData.getContentType() + "\n");

            pageData.getMetaData().forEach((key, value) -> {
                try {
                    String safeKey = key.replace(":", "").replace("\n", "");
                    String safeValue = value.replace("\n", "");
                    writer.write(safeKey + ": " + safeValue + "\n");
                } catch (IOException e) {
                    logger.error("写入元数据时出错，键: {}, 值: {}, 错误: {}", key, value, e.getMessage());
                }
            });
            writer.write("---\n\n");

            // 写入Markdown内容 - 即使为空也写入
            // writer.write(pageData.getMarkdownContent() != null ? pageData.getMarkdownContent() : "无内容");

            // 写入附件信息
            List<PageData> attachments = pageData.getAttachments();
            if (!attachments.isEmpty()) {
                writer.write("\n\n## 附件\n");
                for (PageData attachment : attachments) {
                    try {
                        writer.write("- [" + attachment.getDescription() + "](" + attachment.getLocalPath() + ") (原始链接: " + attachment.getOriginalUrl() + ")\n");
                    } catch (IOException e) {
                        logger.error("写入附件信息时出错，URL: {}, 错误: {}", attachment.getOriginalUrl(), e.getMessage());
                    }
                }
            }

            // 写入外部链接
            List<String> externalLinks = pageData.getExternalLinks();
            if (!externalLinks.isEmpty()) {
                writer.write("\n\n## 外部链接\n");
                for (String link : externalLinks) {
                    try {
                        writer.write("- " + link + "\n");
                    } catch (IOException e) {
                        logger.error("写入外部链接时出错，链接: {}, 错误: {}", link, e.getMessage());
                    }
                }
            }
        }

        logger.info("数据已保存到: {}", filePath);
        return filePath;
    }

    /**
     * 保存附件文件。
     *
     * @param url     附件URL
     * @param content 附件内容
     * @return 保存的文件路径
     * @throws IOException 如果保存过程中发生IO错误
     */
    public Path saveAttachment(String url, byte[] content) throws IOException {
        // 生成保存路径
        //  String rootDir = Paths.get(this.rootDir).toString();
        String fileName = UrlUtils.getFileName(url);
        String localPath = UrlUtils.generateLocalPath(rootDir, url, fileName);

        Path parentDir = Paths.get(localPath).getParent(); // 获取父目录
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
        Path filePath = parentDir.resolve(fileName);
        // 写入文件内容
        Files.write(filePath, content);
        logger.info("附件已保存到: {}", filePath);
        return filePath;
    }
}
