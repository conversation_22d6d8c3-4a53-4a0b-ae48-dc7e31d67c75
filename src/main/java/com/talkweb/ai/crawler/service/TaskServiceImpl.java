package com.talkweb.ai.crawler.service;

import com.talkweb.ai.crawler.coordinator.CrawlCoordinator;
import com.talkweb.ai.crawler.coordinator.ICrawlCallback;
import com.talkweb.ai.crawler.dto.CreateTaskRequest;
import com.talkweb.ai.crawler.dto.TaskDto;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.mapper.TaskMapper;
import com.talkweb.ai.crawler.model.Task;
import com.talkweb.ai.crawler.model.TaskStatus;
import com.talkweb.ai.crawler.repository.TaskRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of the TaskService interface.
 */
@Service
@RequiredArgsConstructor
@Transactional
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final TaskMapper taskMapper;
    private final CrawlCoordinator crawlCoordinator;

    @Override
    public TaskDto createTask(CreateTaskRequest request) {
        Task task = taskMapper.toEntity(request);
        Task savedTask = taskRepository.save(task);
        
        // 异步启动爬取任务
        if (savedTask.getStatus() == TaskStatus.PENDING) {
            startCrawlAsync(savedTask);
        }
        
        return taskMapper.toDto(savedTask);
    }

    private void startCrawlAsync(Task task) {
        ICrawlCallback callback = new ICrawlCallback() {
            @Override
            public void onPageCrawled(PageData pageData) {
                updateTaskProgress(task.getId(), pageData);
            }

            @Override
            public void onError(String url, String message, Exception e) {
                handleCrawlError(task.getId(), url, message);
            }
        };

        crawlCoordinator.startCrawlAsync(task.getUrl(), callback);
    }

    private void updateTaskProgress(UUID taskId, PageData pageData) {
        taskRepository.findById(taskId).ifPresent(task -> {
            // 更新任务进度和状态
            task.setProgress(calculateProgress(task));
            taskRepository.save(task);
        });
    }

    private void handleCrawlError(UUID taskId, String url, String message) {
        taskRepository.findById(taskId).ifPresent(task -> {
            // 处理爬取错误
            task.setStatus(TaskStatus.FAILED);
            task.setEndTime(LocalDateTime.now());
            taskRepository.save(task);
        });
    }

    private int calculateProgress(Task task) {
        // 实现进度计算逻辑
        return 0;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TaskDto> getTaskById(UUID id) {
        return taskRepository.findById(id).map(taskMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TaskDto> getAllTasks(Pageable pageable) {
        return taskRepository.findAll(pageable).map(taskMapper::toDto);
    }

    @Override
    public Optional<TaskDto> startTask(UUID id) {
        return taskRepository.findById(id).map(task -> {
            if (task.getStatus() == TaskStatus.PENDING) {
                task.setStatus(TaskStatus.RUNNING);
                task.setStartTime(LocalDateTime.now());
                // TODO: Trigger async execution here
                return taskMapper.toDto(taskRepository.save(task));
            }
            return taskMapper.toDto(task); // Or throw an exception
        });
    }

    @Override
    public Optional<TaskDto> cancelTask(UUID id) {
        return taskRepository.findById(id).map(task -> {
            if (task.getStatus() == TaskStatus.RUNNING) {
                // 调用爬虫核心取消任务
                crawlCoordinator.shutdown();
                
                task.setStatus(TaskStatus.CANCELLED);
                task.setEndTime(LocalDateTime.now());
                return taskMapper.toDto(taskRepository.save(task));
            }
            return taskMapper.toDto(task);
        });
    }

    @Override
    public Optional<TaskDto> retryTask(UUID id) {
        return taskRepository.findById(id).map(task -> {
            if (task.getStatus() == TaskStatus.FAILED) {
                task.setStatus(TaskStatus.PENDING);
                task.setStartTime(null);
                task.setEndTime(null);
                task.setProgress(0);
                return taskMapper.toDto(taskRepository.save(task));
            }
            return taskMapper.toDto(task); // Or throw an exception
        });
    }
}
