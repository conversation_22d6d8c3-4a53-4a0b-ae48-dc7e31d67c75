package com.talkweb.ai.crawler.service;

import com.talkweb.ai.crawler.dto.CreateTaskRequest;
import com.talkweb.ai.crawler.dto.TaskDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Service interface for managing crawl tasks.
 */
public interface TaskService {

    /**
     * Creates a new crawl task.
     *
     * @param request The request object containing task details.
     * @return The created task's DTO.
     */
    TaskDto createTask(CreateTaskRequest request);

    /**
     * Retrieves a task by its ID.
     *
     * @param id The UUID of the task.
     * @return An Optional containing the task's DTO if found.
     */
    Optional<TaskDto> getTaskById(UUID id);

    /**
     * Retrieves all tasks with pagination.
     *
     * @param pageable The pagination information.
     * @return A Page of task DTOs.
     */
    Page<TaskDto> getAllTasks(Pageable pageable);

    /**
     * Starts a crawl task.
     *
     * @param id The UUID of the task to start.
     * @return An Optional containing the updated task's DTO if found and started.
     */
    Optional<TaskDto> startTask(UUID id);

    /**
     * Cancels a running task.
     *
     * @param id The UUID of the task to cancel.
     * @return An Optional containing the updated task's DTO if found and cancelled.
     */
    Optional<TaskDto> cancelTask(UUID id);

    /**
     * Retries a failed task.
     *
     * @param id The UUID of the task to retry.
     * @return An Optional containing the updated task's DTO if found and requeued.
     */
    Optional<TaskDto> retryTask(UUID id);
}
