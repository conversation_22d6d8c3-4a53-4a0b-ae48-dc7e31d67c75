package com.talkweb.ai.crawler.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import java.util.List;

/**
 * Data Transfer Object for creating a new crawl task.
 */
@Data
public class CreateTaskRequest {

    @NotBlank(message = "URL cannot be blank")
    @URL(message = "Must be a valid URL")
    private String url;

    @Min(value = 0, message = "Max depth must be non-negative")
    private int maxDepth = 3;

    private String outputDir;

    private List<String> allowedDomains;

    @Min(value = 1, message = "Max connections must be at least 1")
    private int maxConnectionsPerDomain = 5;

    @Min(value = 0, message = "Connection timeout must be non-negative")
    private int connectionTimeout = 30000;

    @Min(value = 0, message = "Read timeout must be non-negative")
    private int readTimeout = 60000;

    @Min(value = 0, message = "Max retries must be non-negative")
    private int maxRetries = 3;

    private boolean enableDynamicFetcher = true;

    private String userAgent;

    @URL(message = "Callback URL must be a valid URL")
    private String callbackUrl;
}
