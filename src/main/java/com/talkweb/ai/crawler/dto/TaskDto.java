package com.talkweb.ai.crawler.dto;

import com.talkweb.ai.crawler.model.TaskStatus;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.*;
/**
 * Data Transfer Object for returning task information.
 */
@Data
public class TaskDto {

    private UUID taskId;
    private String url;
    private TaskStatus status;
    private int progress;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private long urlsCrawled;
    private long urlsFailed;
    private String currentUrl;
    private String message;
    private int maxDepth;
    private String outputDir;
    private List<String> allowedDomains;
    private int maxConnectionsPerDomain;
    private boolean enableDynamicFetcher;
    private String callbackUrl;
}
