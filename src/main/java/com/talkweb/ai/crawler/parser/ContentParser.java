package com.talkweb.ai.crawler.parser;

import com.talkweb.ai.crawler.converter.HtmlToMarkdownConverter;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.nio.charset.Charset;
import java.nio.charset.IllegalCharsetNameException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 内容解析器，负责从HTML内容中提取结构化信息。
 */
public class ContentParser {
    /**
     * 可下载的附件文件类型
     */
    public static final Set<String> DOCUMENT_FILE_TYPES = Set.of(
        ".doc", ".docx", ".pdf", ".txt", ".md", ".mdx", ".ppt", ".pptx", ".xls", ".xlsx",
        ".csv", ".json", ".xml", ".dat"
    );
    public static final Set<String> COMPRESSED_FILE_TYPES = Set.of(
        ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"
    );
    public static final Set<String> IMAGE_FILE_TYPES = Set.of(
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".ico"
    );
    public static final Set<String> VIDEO_FILE_TYPES = Set.of(
        ".mp4", ".webm", ".ogg", ".mov", ".avi", ".wmv", ".flv", ".mkv"
    );
    public static final Set<String> AUDIO_FILE_TYPES = Set.of(
        ".mp3", ".wav", ".ogg", ".flac", ".aac", ".m4a", ".webm", ".mp4"
    );
    private static final Logger logger = LoggerFactory.getLogger(ContentParser.class);
    private static final Pattern CHARSET_PATTERN = Pattern.compile(
        "<meta.*?charset=([\\\"']?)([^\\\"'>]+)",
        Pattern.CASE_INSENSITIVE);

    private final HtmlToMarkdownConverter converter;

    // private final FileDownloader fileDownloader;
    /**
     * 可下载的附件文件类型
     */
    private final Set<String> downloadableAttachmentFileTypes = new CopyOnWriteArraySet<>(DOCUMENT_FILE_TYPES);
    private boolean html2markdown = true;
    /**
     * 只是提取相关内容，不实际下载附件
     */
    private boolean parseAttachmentsOnly = false;

    public ContentParser(String outputDir) {
        this.converter = new HtmlToMarkdownConverter();
        //  this.fileDownloader = new FileDownloader(outputDir);
    }

    public boolean isHtml2markdown() {
        return html2markdown;
    }

    public void setHtml2markdown(boolean html2markdown) {
        this.html2markdown = html2markdown;
    }

    public boolean isParseAttachmentsOnly() {
        return parseAttachmentsOnly;
    }

    public void setParseAttachmentsOnly(boolean parseAttachmentsOnly) {
        this.parseAttachmentsOnly = parseAttachmentsOnly;
    }

    public void addDownloadableAttachmentFileType(String fileType) {
        if (!fileType.startsWith(".")) {
            fileType = "." + fileType;
        }
        downloadableAttachmentFileTypes.add(fileType.toLowerCase());
    }

    public void removeDownloadableAttachmentFileType(String fileType) {
        if (!fileType.startsWith(".")) {
            fileType = "." + fileType;
        }
        downloadableAttachmentFileTypes.remove(fileType);
    }

    public void setDownloadableAttachmentFileTypes(Set<String> downloadableAttachmentFileTypes) {
        this.downloadableAttachmentFileTypes.clear();
        this.downloadableAttachmentFileTypes.addAll(downloadableAttachmentFileTypes);
    }

    public void setDownloadableAttachmentFileTypes(String types) {
        this.downloadableAttachmentFileTypes.clear();
        Arrays.stream(types.split(",")).forEach(type -> {
            if (!type.startsWith(".")) {
                type = "." + type;
            }
            this.downloadableAttachmentFileTypes.add(type);
        });
    }

    /**
     * 解析HTML内容，提取页面数据。
     */
    public PageData parse(PageData pageData) {
        String htmlContent = pageData.getHtmlContent();
        String url = pageData.getUrl();
        return parse(pageData, htmlContent, url);
    }

    /**
     * 解析HTML内容，提取页面数据。
     */
    public PageData parse(String html, String url) {
        PageData pageData = new PageData();

        return parse(pageData, html, url);
    }

    /**
     * 解析HTML内容，提取页面数据。
     */
    public PageData parse(PageData pageData, String html, String url) {
        //  PageData pageData = new PageData();
        pageData.setUrl(url);
        pageData.setFetchTimestamp(LocalDateTime.now());
        pageData.setHtmlContent(html);
        try {
            String encoding = detectEncoding(html);
            Document doc = parseDocument(html, url, encoding);

            extractBasicInfo(pageData, doc);

            extractLinks(pageData, doc, url);

            //downloadAttachments(pageData, doc, url);

            if (isHtml2markdown()) {
                pageData.setMarkdownContent(convertToMarkdown(doc, url));
            }
            pageData.setProcessed(true);

        } catch (Exception e) {
            logger.error("解析HTML内容时出错: {}", url, e);
            // pageData.setMarkdownContent("解析错误: " + e.getMessage());
            pageData.setProcessed(false);
        }

        return pageData;
    }


    private Document parseDocument(String html, String url, String encoding) throws Exception {
        if (html == null) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        if (url == null || url.isEmpty()) {
            return Jsoup.parse(html);
        }
        return Jsoup.parse(new ByteArrayInputStream(html.getBytes(encoding)), encoding, url);
    }

    private void extractBasicInfo(PageData pageData, Document doc) {
        pageData.setTitle(Optional.ofNullable(doc.selectFirst("title"))
            .map(Element::text)
            .orElse("无标题"));

        Map<String, String> metaData = new HashMap<>();
        doc.select("meta").forEach(meta -> {
            String name = meta.attr("name");
            String property = meta.attr("property");
            String content = meta.attr("content");

            if (!name.isEmpty()) metaData.put(name, content);
            if (!property.isEmpty()) metaData.put(property, content);
        });
        pageData.setMetaData(metaData);
    }

    /**
     * 提取页面中的链接
     *
     * @param pageData 页面数据
     * @param doc      HTML文档
     * @param baseUrl  基础URL
     */
    public void extractLinks(PageData pageData, Document doc, String baseUrl) {
        //head>base href 处理
        /*
          <head>
                <base href="http://another-domain.com/base/">
            </head>
         */
        String baseDomain = UrlUtils.extractDomain(baseUrl);
        Element baseElement = doc.selectFirst("head > base[href]");
        if (baseElement != null) {
            String baseHref = baseElement.attr("href");
            if (!baseHref.isEmpty()) {
                String domain = UrlUtils.extractDomain(baseHref);
                baseUrl = baseHref;
                if (!domain.isEmpty() && !domain.equalsIgnoreCase(baseDomain)) {
                    logger.warn("Base href {} 与当前页面域名 {} 不匹配，可能导致链接解析错误", baseHref, baseDomain);
                    baseDomain = domain; // 更新为base href的域名
                }
            }
        }


        Set<String> internalLinks = new LinkedHashSet<>();
        Set<String> externalLinks = new LinkedHashSet<>();
        final String finalBaseUrl = baseUrl;
        final String finalBaseDomain = baseDomain;
        doc.select("a[href]").stream()
            .map(link -> link.attr("href")) // 原始值，支持相对路径
            .filter(href -> href != null && !href.isEmpty()) // 过滤空链接
            .map(href -> UrlUtils.normalizeUrl(UrlUtils.resolveRelativeUrl(finalBaseUrl, href))) // 规范化绝对链接
            .filter(Objects::nonNull)
            .forEach(url -> {

                //是否是有效的HTTP链接
                if (!UrlUtils.isHttpLink(url)) {
                    return;
                }
                //排除可下载的附件（附件不算作普通链接）
//                if (UrlUtils.isDownloadable(url, downloadableAttachmentFileTypes)) {
//                    return;
//                }
                //分类为内部或外部链接
                String domain = UrlUtils.extractDomain(url);
                if (domain.equalsIgnoreCase(finalBaseDomain)) {
                    internalLinks.add(url);
                } else {
                    externalLinks.add(url);
                }
            });

        //iframe链接也算作内部链接
        doc.select("iframe[src]").stream()
            .map(iframe -> iframe.attr("src"))
            .filter(href -> !href.isEmpty())
            .map(href -> UrlUtils.normalizeUrl(UrlUtils.resolveRelativeUrl(finalBaseUrl, href)))
            .filter(Objects::nonNull)
            .forEach(url -> {
                if (!UrlUtils.isHttpLink(url)) {
                    return;
                }
                //排除可下载的附件（附件不算作普通链接）
//                if (UrlUtils.isDownloadable(url, downloadableAttachmentFileTypes)) {
//                    return;
//                }
                //分类为内部或外部链接
                String domain = UrlUtils.extractDomain(url);
                if (domain.equalsIgnoreCase(finalBaseDomain)) {
                    internalLinks.add(url);
                } else {
                    externalLinks.add(url);
                }
            });
        internalLinks.forEach(pageData::addInternalLink);
        externalLinks.forEach(pageData::addExternalLink);
    }


    public boolean isDownloadableAttachment(String url) {
        return downloadableAttachmentFileTypes.contains(UrlUtils.getFileExtension(url));
    }

    /**
     * 将HTML文档转换为Markdown格式
     *
     * @param doc HTML文档
     * @param url 页面URL
     * @return Markdown格式的字符串
     */
    private String convertToMarkdown(Document doc, String url) {
        try {
            return converter.convert(doc);
        } catch (Exception e) {
            logger.error("转换HTML到Markdown时出错: {}", url, e);
            //转换错误，返回html
            return doc.html();
        }
    }

    /**
     * 检测HTML文档的编码
     *
     * @param html HTML文档内容
     * @return 编码名称
     */
    private String detectEncoding(String html) {
        try {
            Matcher matcher = CHARSET_PATTERN.matcher(html);
            if (matcher.find()) {
                String encoding = matcher.group(2).trim().toUpperCase();
                if (Charset.isSupported(encoding)) {
                    return encoding;
                }
            }
            return StandardCharsets.UTF_8.name();
        } catch (IllegalCharsetNameException e) {
            logger.warn("检测编码失败，使用默认UTF-8: {}", e.getMessage());
            return StandardCharsets.UTF_8.name();
        }
    }
}
