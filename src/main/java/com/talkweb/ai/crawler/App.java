package com.talkweb.ai.crawler;

import java.util.Arrays;

/**
 * Unified application entry point.
 * Determines whether to run in server mode or command-line (CLI) mode based on arguments.
 */
public class App {

    /**
     * The main method that serves as the single entry point for the application.
     *
     * @param args Command line arguments.
     */
    public static void main(String[] args) {
        boolean isServerMode = Arrays.asList(args).contains("-server");

        if (isServerMode) {
            System.out.println("Starting in server mode...");
            CrawlerApplication.main(args);
        } else {
            System.out.println("Starting in command-line mode...");
            CrawlerCli.main(args);
        }
    }
}
