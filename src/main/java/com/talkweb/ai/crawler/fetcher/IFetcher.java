package com.talkweb.ai.crawler.fetcher;

import com.talkweb.ai.crawler.model.PageData;

import java.io.IOException;

/**
 * Interface for page fetching components.
 * Implementations are responsible for retrieving the content of a web page given its URL.
 * It extends AutoCloseable to ensure proper resource management.
 */
public interface IFetcher extends AutoCloseable {

    /**
     * Fetches the content of the web page at the given URL.
     *
     * @param url The URL of the page to fetch.
     * @return The content of the page as a String, or null if fetching fails.
     */
    PageData fetchPageContent(String url) throws IOException;

    /**
     * Closes the fetcher and releases any underlying resources.
     * This method is called when the fetcher is no longer needed.
     * @throws Exception if an error occurs during closing.
     */
    @Override
    void close() throws Exception;
}
