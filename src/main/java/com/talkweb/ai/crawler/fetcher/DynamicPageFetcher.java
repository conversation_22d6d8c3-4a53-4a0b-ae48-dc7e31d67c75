package com.talkweb.ai.crawler.fetcher;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.talkweb.ai.crawler.config.CrawlerConfig;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

public class DynamicPageFetcher implements IFetcher {

    private static final Logger logger = LoggerFactory.getLogger(DynamicPageFetcher.class);
    private final Playwright playwright;
    private final Browser browser;
    private final CrawlerConfig config;


    // 可以考虑从配置中读取这些参数
    public DynamicPageFetcher(final CrawlerConfig config) {
        this.config = config;
        this.playwright = Playwright.create();

        BrowserType browserType = switch (config.getPlaywrightBrowserType().toLowerCase()) {
            case "firefox" -> playwright.firefox();
            case "webkit" -> playwright.webkit();
            default -> playwright.chromium();
        };
        BrowserType.LaunchOptions options = new BrowserType.LaunchOptions()
            .setHeadless(config.isPlaywrightHeadless())
            .setTimeout(config.getPlaywrightTimeout())
            //.setProxy()
            ;

        this.browser = browserType.launch(options);
        logger.info("DynamicPageFetcher initialized with Playwright and {} browser (Headless: {}).",
            browser.browserType().name(), config.isPlaywrightHeadless());
    }

    // (可选) 主方法用于快速测试
    public static void main(String[] args) {
        // 示例：需要一个能动态加载内容的网站进行测试
        String testUrl = "https://www.whatismybrowser.com/detect/what-is-my-user-agent"; // 或者其他JS渲染的网站

        // This main method is for basic testing and does not use the full config system.
        // For proper use, instantiate through CrawlCoordinator with a full CrawlerConfig.
        CrawlerConfig testConfig = new CrawlerConfig.Builder().build(); // Using default config for test
        try (DynamicPageFetcher fetcher = new DynamicPageFetcher(testConfig)) {
            PageData pageData = fetcher.fetchPageContent(testUrl);
            if (pageData != null) {
                String content = pageData.getHtmlContent();
                System.out.println("Fetched Content (first 500 chars):");
                System.out.println(content.substring(0, Math.min(content.length(), 500)));
            } else {
                System.out.println("Failed to fetch content for " + testUrl);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从指定URL获取网页内容。
     *
     * @param urlStr 网页URL
     * @return 网页内容字符串
     * @throws IOException 如果抓取过程中发生IO错误
     */
    public PageData fetchPageContent(String urlStr) throws IOException {
        int retries = 0;
        IOException lastException = null;
        PageData ret;
        if (urlStr == null || urlStr.trim().isEmpty()) {
            throw new IllegalArgumentException("URL不能为空或空白");
        }
        try {
            URL url = UrlUtils.newURL(urlStr);
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("无效URL: " + urlStr, e);
        }
        while (retries < config.getMaxRetries()) {
            ret = fetchPageContentInternal(urlStr);
            if (ret != null) {
                return ret; // 成功获取内容，直接返回
            }
            logger.debug("Fetch failed for URL: {}, retrying... (attempt {}/{})", urlStr, retries + 1, config.getMaxRetries());
            retries++;
            if (retries < config.getMaxRetries()) {
                // 在重试前等待一段时间，采用指数退避策略
                try {
                    TimeUnit.MILLISECONDS.sleep((long) Math.pow(2, retries) * 1000); // 等待2^retries秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                }
            }
        }
        return null;
    }

    public PageData fetchPageContentInternal(String url) {
        if (url == null || url.trim().isEmpty()) {
            logger.warn("URL is null or empty, cannot fetch content.");
            return null;
        }

        // BrowserContext 提供了隔离的环境，类似于浏览器的隐身模式
        // 建议为每个请求或一组相关请求创建一个新的 context
        try (final BrowserContext context = browser.newContext();
             final Page page = context.newPage()) {

            final AtomicReference<Response> finalResponse = new AtomicReference<>();
            page.onResponse(response -> {
                // 监听主文档的响应，确保是主请求的响应
                if (response.request().isNavigationRequest() && response.url().equals(url)) {
                    finalResponse.set(response);
                }
            });

            logger.info("Navigating to URL: {}", url);
            page.navigate(url, new Page.NavigateOptions().setTimeout(config.getPlaywrightTimeout()));

            // 等待网络空闲，表明主要的动态内容可能已加载完成
            page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(config.getPlaywrightTimeout()));

            PageData pageData = new PageData();
            pageData.setUrl(url);
            pageData.setTitle(page.title());
            pageData.setHtmlContent(page.content());
            pageData.setFetchTimestamp(LocalDateTime.now());
            pageData.setProcessed(false); // 初始状态为未处理

            // 从捕获的响应中获取最后修改时间
            Response capturedResponse = finalResponse.get();
            if (capturedResponse != null) {
                String lastModifiedHeader = capturedResponse.headers().get("last-modified");
                if (lastModifiedHeader != null) {
                    // 解析HTTP日期格式，这里需要一个辅助方法或库
                    // 为了简化，我们暂时直接存储字符串，实际应用中应解析为时间戳
                    // pageData.setLastModified(parseHttpDate(lastModifiedHeader));
                    logger.debug("Captured Last-Modified header: {}", lastModifiedHeader);
                } else {
                    logger.debug("No Last-Modified header found for URL: {}", url);
                }
            } else {
                logger.warn("Could not capture the main document response for URL: {}", url);
            }

            logger.info("Successfully fetched content for URL: {}", url);
            return pageData;
        } catch (PlaywrightException e) {
            logger.error("PlaywrightException while fetching URL {}: {}", url, e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected exception while fetching URL {}: {}", url, e.getMessage(), e);
        }
        return null; // 或者抛出自定义异常
    }

    @Override
    public void close() {
        if (browser != null && browser.isConnected()) {
            browser.close();
            logger.info("Browser closed for DynamicPageFetcher.");
        }
        if (playwright != null) {
            playwright.close();
            logger.info("Playwright instance closed for DynamicPageFetcher.");
        }
    }
}
