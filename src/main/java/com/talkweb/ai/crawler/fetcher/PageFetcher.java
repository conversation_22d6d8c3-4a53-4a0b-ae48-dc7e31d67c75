package com.talkweb.ai.crawler.fetcher;

import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * 页面抓取器，负责从指定URL获取网页内容。
 */
public class PageFetcher implements IFetcher {
    public static final int CONNECT_TIMEOUT = 5000; // 连接超时时间，单位：毫秒
    public static final int READ_TIMEOUT = 10000;   // 读取超时时间，单位：毫秒
    public static final int MAX_RETRIES = 3;        // 最大重试次数
    private static final Logger logger = LoggerFactory.getLogger(PageFetcher.class);
    private  int connectionTimeout;
    private  int readTimeout;
    private  int maxRetries;
    private  long maxFileSize; // 最大文件大小，10MB
    private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";

    /**
     *
     */
    public PageFetcher() {
        this(CONNECT_TIMEOUT, READ_TIMEOUT, MAX_RETRIES);
    }

    public PageFetcher(int connectionTimeout, int readTimeout, int maxRetries) {
        this(connectionTimeout, readTimeout, maxRetries, 10 * 1024 * 1024, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    }

    public PageFetcher(int connectionTimeout, int readTimeout, int maxRetries, long maxFileSize, String userAgent) {
        this.connectionTimeout = connectionTimeout;
        this.readTimeout = readTimeout;
        this.maxRetries = maxRetries;
        this.maxFileSize = maxFileSize;
        this.userAgent = userAgent;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    /**
     * 从指定URL获取网页内容。
     *
     * @param urlStr 网页URL
     * @return 网页内容字符串
     * @throws IOException 如果抓取过程中发生IO错误
     */
    public PageData fetchPageContent(String urlStr) throws IOException {
        int retries = 0;
        IOException lastException = null;
        PageData ret;
        if (urlStr == null || urlStr.isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }
        try {
            URL url = UrlUtils.newURL(urlStr);
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("无效URL: " + urlStr, e);
        }
        while (retries < maxRetries) {
            try {
                PageData pageData = fetchPageContentNoRetry(urlStr);
                if (pageData != null) {
                    return pageData; // 成功获取内容，直接返回
                } else {
                    logger.warn("抓取失败，返回null，重试次数: {}/{}", retries + 1, maxRetries);
                }
            } catch (MalformedURLException e) {
                throw new IllegalArgumentException("无效URL: " + urlStr, e);
            } catch (IOException e) {
                lastException = e;
                retries++;
                if (retries < maxRetries) {

                    try {
                        // 在重试前等待一段时间，采用指数退避策略
                        //TimeUnit.MILLISECONDS.sleep(1000L * retries);
                        TimeUnit.MILLISECONDS.sleep((long) Math.pow(2, retries) * 1000); // 等待2^retries秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Interrupted during retry wait", ie);
                    }
                }
                // 记录网络异常到日志
                logger.error("网络异常，尝试次数 {}/{}: {}, 错误: {}", retries, maxRetries, urlStr, e.getMessage());
            }
        }

        throw new IOException("Failed to fetch URL after " + maxRetries + " retries: " + urlStr, lastException);
    }

    public PageData fetchPageContentNoRetry(String urlStr) throws IOException {

        final URL url = UrlUtils.newURL(urlStr);
        final HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        try {
            // 设置连接和读取超时
            connection.setConnectTimeout(connectionTimeout);
            connection.setReadTimeout(readTimeout);

            // 设置请求头，模拟浏览器访问
            connection.setRequestProperty("User-Agent", userAgent);

            // 获取响应码
            int responseCode = connection.getResponseCode();
            long lastModified = connection.getLastModified(); // 触发连接，获取响应头
            PageData ret = new PageData();
            ret.setUrl(urlStr);
            ret.setResponseCode(responseCode);

            ret.setLastModified(lastModified);
            ret.setProcessed(false); // 初始状态为未处理
            ret.setTitle(connection.getHeaderField("Title")); // 尝试获取标题

            if (responseCode != HttpURLConnection.HTTP_OK) {
                logger.error("HTTP请求失败，响应码: {}, URL: {}", responseCode, urlStr);
                throw new IOException("HTTP response code: " + responseCode);
            }
            long contentLengthLong = connection.getContentLengthLong();
            if (contentLengthLong > maxFileSize) {
                connection.disconnect();
                logger.warn("文件大小超过限制: {} bytes, URL: {},length:{} > {} ", contentLengthLong, urlStr, contentLengthLong, maxFileSize);
                return null; // 如果文件大小超过限制，直接返回null
            }
            String contentType = connection.getContentType();
            if (contentType != null && !contentType.startsWith("text/html") && !contentType.startsWith("application/xhtml+xml")) {
                logger.warn("非HTML内容: {}, Content-Type: {}", urlStr, contentType);
            }
            String contentEncoding = connection.getContentEncoding();
            if (contentEncoding != null && !contentEncoding.isEmpty()) {
                logger.info("Content-Encoding: {}", contentEncoding);
            } else {
                contentEncoding = "UTF-8"; // 默认编码
            }
            Charset charset;
            try {
                charset = Charset.forName(contentEncoding); // 默认字符集
            } catch (Exception e) {
                logger.warn("无法识别的字符集: {}, 使用默认UTF-8", contentEncoding);
                charset = StandardCharsets.UTF_8; // 如果无法识别，使用UTF-8
            }
            // 读取网页内容
            String content = IOUtils.toString(connection.getInputStream(), charset);
            connection.disconnect();


            ret.setHtmlContent(content);
            ret.setContentType(contentType);
            ret.setContentLength(contentLengthLong);

            if (ret.getContentLength() <= 0) {
                ret.setContentLength(content.length());
            }
            logger.info("Successfully fetched URL: {}", urlStr);
            return ret;
        } finally {
            // 确保连接被断开
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    @Override
    public void close() throws Exception {
        // No specific resources to close for the basic PageFetcher.
        logger.info("PageFetcher closed.");
    }
}
