package com.talkweb.ai.crawler.model;

/**
 * Represents the lifecycle status of a crawl task.
 */
public enum TaskStatus {
    /**
     * The task has been created but is waiting to be executed.
     */
    PENDING,

    /**
     * The task is currently being executed.
     */
    RUNNING,

    /**
     * The task has finished successfully.
     */
    COMPLETED,

    /**
     * The task terminated with an error.
     */
    FAILED,

    /**
     * The task was cancelled by a user.
     */
    CANCELLED
}
