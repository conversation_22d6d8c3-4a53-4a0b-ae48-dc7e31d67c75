package com.talkweb.ai.crawler.model;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 页面数据模型，封装页面抓取和解析的所有信息。
 */
public class PageData {

    /**
     * 页面URL
     */
    private String url;
    /**
     * 页面标题
     */
    private String title;

    /**
     * 本地保存路径
     * 如果为空，则表示未保存到本地
     */
    private String localPath; // 本地保存路径

    /**
     * 原始HTML内容
     * 用于存储抓取到的原始HTML页面内容
     */
    private String htmlContent; // 原始HTML内容

    /**
     * Markdown内容
     * 用于存储转换后的Markdown格式内容
     */
    private String markdownContent;
    /**
     * meta数据
     */
    private Map<String, String> metaData;
    /**
     * 内部链接列表
     */
    private List<String> internalLinks;
    /**
     * 外部链接列表
     */
    private List<String> externalLinks;

    private Map<String, PageData> pageDatas = new ConcurrentHashMap<>(); // 存储页面数据的映射
    /**
     * 附件信息列表
     */
    private List<PageData> attachments;
    /**
     * 抓取时间戳
     */
    private LocalDateTime fetchTimestamp = LocalDateTime.now(); // 抓取时间戳

    private long contentLength; // 附件内容长度，单位为字节

    /**
     * 最后修改时间
     */
    private long lastModified;  // 最后修改时间

    /**
     * 是否已处理
     */
    private boolean processed;

    private int responseCode; // HTTP状态码，表示抓取结果的状态

    private String contentType; // 附件内容类型，如application/pdf, text/plain等

    private String  description ;
    /**
     * 构造函数，初始化页面数据对象。
     */
    public PageData() {
        this.metaData = new HashMap<>();
        this.internalLinks = new ArrayList<>();
        this.externalLinks = new ArrayList<>();
        this.attachments = new ArrayList<>();
        this.processed = false;
    }
    /**
     * 构造函数，初始化页面数据对象。
     */
    public PageData(String url,String localPath) {
        this.metaData = new HashMap<>();
        this.internalLinks = new ArrayList<>();
        this.externalLinks = new ArrayList<>();
        this.attachments = new ArrayList<>();
        this.url = url;
        this.localPath = localPath;
        this.processed = false;
    }
    /**
     * 构造函数，初始化页面数据对象。
     */
    public PageData(String url,String localPath,String html) {
        this.metaData = new HashMap<>();
        this.internalLinks = new ArrayList<>();
        this.externalLinks = new ArrayList<>();
        this.attachments = new ArrayList<>();
        this.url = url;
        this.localPath = localPath;
        this.processed = false;
        this.htmlContent = html;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public long getContentLength() {
        return contentLength;
    }

    public void setContentLength(long contentLength) {
        this.contentLength = contentLength;
    }

    public int getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }
// Getters and Setters

    public String getHtmlContent() {
        return htmlContent;
    }

    public void setHtmlContent(String htmlContent) {
        this.htmlContent = htmlContent;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public long getLastModified() {
        return lastModified;
    }

    public void setLastModified(long lastModified) {
        this.lastModified = lastModified;
    }

    public String getUrl() {
        return url;
    }

    public String getOriginalUrl() {
        return url;
    }
    public void setUrl(String url) {
        this.url = url;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMarkdownContent() {
        return markdownContent;
    }

    public void setMarkdownContent(String markdownContent) {
        this.markdownContent = markdownContent;
    }

    public Map<String, String> getMetaData() {
        return metaData;
    }

    public void setMetaData(Map<String, String> metaData) {
        this.metaData = metaData;
    }

    public List<String> getInternalLinks() {
        return internalLinks;
    }

    public void setInternalLinks(List<String> internalLinks) {
        this.internalLinks = internalLinks;
    }

    public void addInternalLink(String internalLink) {
        this.internalLinks.add(internalLink);
    }

    public void addExternalLink(String externalLink) {
        this.externalLinks.add(externalLink);
    }

    public List<String> getExternalLinks() {
        return externalLinks;
    }

    public void setExternalLinks(List<String> externalLinks) {
        this.externalLinks = externalLinks;
    }

    /**
     * 添加附件信息
     *
     * @param attachment 附件信息对象
     */
    public void addAttachment(PageData attachment) {
        this.attachments.add(attachment);
    }

    /**
     * 获取附件列表
     *
     * @return 附件列表
     */
    public List<PageData> getAttachments() {
        return attachments;
    }

    /**
     * 设置附件列表
     *
     * @param attachments 附件列表
     */
    public void setAttachments(List<PageData> attachments) {
        this.attachments = attachments;
    }

    public LocalDateTime getFetchTimestamp() {
        return fetchTimestamp;
    }

    public void setFetchTimestamp(LocalDateTime fetchTimestamp) {
        this.fetchTimestamp = fetchTimestamp;
    }

    public boolean isProcessed() {
        return processed;
    }

    public void setProcessed(boolean processed) {
        this.processed = processed;
    }

    public void addPageData(String link, PageData next) {
        pageDatas.put(link, next);
    }
    public Map<String, PageData> getPageDatas() {
        return pageDatas;
    }
    public void setPageDatas(Map<String, PageData> pageDatas) {
        this.pageDatas = pageDatas;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof PageData pageData)) return false;
        return responseCode == pageData.responseCode && Objects.equals(url, pageData.url) && Objects.equals(localPath, pageData.localPath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(url, localPath, responseCode);
    }

    @Override
    public String toString() {
        return "PageData{" +
            "url='" + url + '\'' +
            ", title='" + title + '\'' +
            ", localPath='" + localPath + '\'' +
            ", htmlContent='" + htmlContent + '\'' +
            ", markdownContent='" + markdownContent + '\'' +
            ", metaData=" + metaData +
            ", internalLinks=" + internalLinks +
            ", externalLinks=" + externalLinks +
            ", pageDatas=" + pageDatas +
            ", attachments=" + attachments +
            ", fetchTimestamp=" + fetchTimestamp +
            ", contentLength=" + contentLength +
            ", lastModified=" + lastModified +
            ", processed=" + processed +
            ", responseCode=" + responseCode +
            ", contentType='" + contentType + '\'' +
            ", description='" + description + '\'' +
            '}';
    }
}
