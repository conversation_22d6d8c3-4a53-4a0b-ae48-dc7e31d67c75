<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::section})}">
<head>
    <title>Task Details</title>
</head>
<body>
    <section>
        <div class="d-flex justify-content-between mb-3">
            <h2>Task Details</h2>
            <a href="/tasks" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to List
            </a>
        </div>

        <div class="card mb-3">
            <div class="card-header">
                <h5 th:text="'Task ID: ' + ${task.taskId}"></h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>URL:</strong> <span th:text="${task.url}"></span></p>
                        <p><strong>Status:</strong> 
                            <span th:text="${task.status}" 
                                  th:classappend="${task.status == 'RUNNING'} ? 'badge bg-primary' : 
                                                 ${task.status == 'COMPLETED'} ? 'badge bg-success' : 
                                                 ${task.status == 'FAILED'} ? 'badge bg-danger' : 'badge bg-secondary'">
                            </span>
                        </p>
                        <p><strong>Start Time:</strong> <span th:text="${#temporals.format(task.startTime, 'yyyy-MM-dd HH:mm')}"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Progress:</strong></p>
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" 
                                 th:style="'width:' + ${task.progress} + '%'" 
                                 th:aria-valuenow="${task.progress}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100"
                                 th:text="${task.progress} + '%'">
                            </div>
                        </div>
                        <p><strong>URLs Crawled:</strong> <span th:text="${task.urlsCrawled}"></span></p>
                        <p><strong>URLs Failed:</strong> <span th:text="${task.urlsFailed}"></span></p>
                    </div>
                </div>

                <div th:if="${task.status == 'RUNNING'}" class="text-center">
                    <a th:href="@{/tasks/{id}/cancel(id=${task.taskId})}" class="btn btn-danger">
                        <i class="bi bi-x-circle"></i> Cancel Task
                    </a>
                </div>
            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header">
                <h5>URL List</h5>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="urlTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="all-tab" data-bs-toggle="tab" 
                                data-bs-target="#all" type="button" role="tab">All URLs</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="success-tab" data-bs-toggle="tab" 
                                data-bs-target="#success" type="button" role="tab">Success</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="failed-tab" data-bs-toggle="tab" 
                                data-bs-target="#failed" type="button" role="tab">Failed</button>
                    </li>
                </ul>
                <div class="tab-content p-3 border border-top-0 rounded-bottom">
                    <div class="tab-pane fade show active" id="all" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>URL</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="url : ${urls}">
                                        <td th:text="${url.url}"></td>
                                        <td>
                                            <span th:if="${url.status == 'SUCCESS'}" class="badge bg-success">Success</span>
                                            <span th:if="${url.status == 'FAILED'}" class="badge bg-danger">Failed</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="success" role="tabpanel">
                        <!-- Success URLs content -->
                    </div>
                    <div class="tab-pane fade" id="failed" role="tabpanel">
                        <!-- Failed URLs content -->
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>Error Logs</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>URL</th>
                                <th>Error</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="error : ${errors}">
                                <td th:text="${#temporals.format(error.timestamp, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                <td th:text="${error.url}"></td>
                                <td th:text="${error.message}"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <script th:inline="javascript">
        /*<![CDATA[*/
        // Auto-refresh if task is running
        if (/*[[${task.status == 'RUNNING'}]]*/ false) {
            setTimeout(function() {
                window.location.reload();
            }, 5000); // Refresh every 5 seconds
        }
        /*]]>*/
    </script>
</body>
</html>