<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::section})}">
<head>
    <title>New Crawl Task</title>
</head>
<body>
    <section>
        <div class="d-flex justify-content-between mb-3">
            <h2>New Crawl Task</h2>
            <a href="/tasks" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to List
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <form th:action="@{/api/tasks}" method="post" id="taskForm">
                    <div class="mb-3">
                        <label for="url" class="form-label">URL</label>
                        <input type="url" class="form-control" id="url" name="url" required
                               th:classappend="${#fields.hasErrors('url')} ? 'is-invalid'">
                        <div class="invalid-feedback" th:if="${#fields.hasErrors('url')}" 
                             th:errors="*{url}">URL error</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="maxDepth" class="form-label">Max Depth</label>
                            <input type="number" class="form-control" id="maxDepth" name="maxDepth" 
                                   min="1" max="10" value="3">
                        </div>
                        <div class="col-md-3">
                            <label for="maxConnections" class="form-label">Max Connections</label>
                            <input type="number" class="form-control" id="maxConnections" 
                                   name="maxConnectionsPerDomain" min="1" max="20" value="5">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="allowedDomains" class="form-label">Allowed Domains (comma separated)</label>
                        <input type="text" class="form-control" id="allowedDomains" name="allowedDomains">
                    </div>

                    <div class="mb-3">
                        <label for="outputDir" class="form-label">Output Directory</label>
                        <input type="text" class="form-control" id="outputDir" name="outputDir" 
                               th:value="${defaultOutputDir}">
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="enableDynamic" name="enableDynamicFetcher">
                        <label class="form-check-label" for="enableDynamic">Enable Dynamic Rendering</label>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i> Create Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <script>
        document.getElementById('taskForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                url: formData.get('url'),
                maxDepth: parseInt(formData.get('maxDepth')),
                maxConnectionsPerDomain: parseInt(formData.get('maxConnectionsPerDomain')),
                outputDir: formData.get('outputDir'),
                enableDynamicFetcher: formData.get('enableDynamicFetcher') === 'on',
                allowedDomains: formData.get('allowedDomains') 
                    ? formData.get('allowedDomains').split(',').map(d => d.trim()) 
                    : []
            };

            fetch('/api/tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                window.location.href = '/tasks/' + data.taskId;
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>