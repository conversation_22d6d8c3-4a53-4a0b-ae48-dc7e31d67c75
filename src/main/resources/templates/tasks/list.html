<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::section})}">
<head>
    <title>Task List</title>
</head>
<body>
    <section>
        <div class="d-flex justify-content-between mb-3">
            <h2>Task List</h2>
            <a href="/tasks/new" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> New Task
            </a>
        </div>

        <div class="card mb-3">
            <div class="card-body">
                <form th:action="@{/tasks}" method="get" class="row g-3">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option th:each="status : ${T(com.talkweb.ai.crawler.model.TaskStatus).values()}"
                                    th:value="${status}" 
                                    th:text="${status}"
                                    th:selected="${param.status != null && param.status == status}">
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">Filter</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>URL</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th>Start Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="task : ${tasks.content}">
                        <td th:text="${task.taskId}"></td>
                        <td th:text="${task.url}"></td>
                        <td>
                            <span th:text="${task.status}" 
                                  th:classappend="${task.status == 'RUNNING'} ? 'badge bg-primary' : 
                                                 ${task.status == 'COMPLETED'} ? 'badge bg-success' : 
                                                 ${task.status == 'FAILED'} ? 'badge bg-danger' : 'badge bg-secondary'">
                            </span>
                        </td>
                        <td>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" 
                                     th:style="'width:' + ${task.progress} + '%'" 
                                     th:aria-valuenow="${task.progress}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100"
                                     th:text="${task.progress} + '%'">
                                </div>
                            </div>
                        </td>
                        <td th:text="${#temporals.format(task.startTime, 'yyyy-MM-dd HH:mm')}"></td>
                        <td>
                            <a th:href="@{/tasks/{id}(id=${task.taskId})}" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a th:if="${task.status == 'RUNNING'}" 
                               th:href="@{/tasks/{id}/cancel(id=${task.taskId})}" 
                               class="btn btn-sm btn-warning">
                                <i class="bi bi-x-circle"></i>
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div th:if="${tasks.totalPages > 1}" class="d-flex justify-content-center">
            <nav>
                <ul class="pagination">
                    <li class="page-item" th:classappend="${tasks.first} ? 'disabled'">
                        <a class="page-link" th:href="@{/tasks(size=${tasks.size},page=0,status=${param.status})}">First</a>
                    </li>
                    <li class="page-item" th:classappend="${tasks.first} ? 'disabled'">
                        <a class="page-link" th:href="@{/tasks(size=${tasks.size},page=${tasks.number-1},status=${param.status})}">Previous</a>
                    </li>
                    <li class="page-item" th:each="i : ${#numbers.sequence(0, tasks.totalPages-1)}" 
                        th:classappend="${i == tasks.number} ? 'active'">
                        <a class="page-link" th:href="@{/tasks(size=${tasks.size},page=${i},status=${param.status})}" th:text="${i+1}"></a>
                    </li>
                    <li class="page-item" th:classappend="${tasks.last} ? 'disabled'">
                        <a class="page-link" th:href="@{/tasks(size=${tasks.size},page=${tasks.number+1},status=${param.status})}">Next</a>
                    </li>
                    <li class="page-item" th:classappend="${tasks.last} ? 'disabled'">
                        <a class="page-link" th:href="@{/tasks(size=${tasks.size},page=${tasks.totalPages-1},status=${param.status})}">Last</a>
                    </li>
                </ul>
            </nav>
        </div>
    </section>
</body>
</html>