package com.talkweb.ai.crawler.scenarios;

import com.talkweb.ai.crawler.core.CrawlerMain;
import com.talkweb.ai.crawler.robustness.SimpleWebServer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class NetworkScenarioTest {

    private static final String TEST_SITE_DIR = "test-site-network";
    @TempDir
    Path tempDir;
    private SimpleWebServer server;

    @BeforeEach
    void setUp() throws IOException {
        Path sitePath = tempDir.resolve(TEST_SITE_DIR);
        Files.createDirectories(sitePath);

        Files.writeString(sitePath.resolve("index.html"), "<html><body><a href='page1.html'>Page 1</a></body></html>");
        Files.writeString(sitePath.resolve("page1.html"), "<html><body>Hello</body></html>");

        server = new SimpleWebServer(tempDir, 0);
        server.start();
    }

    @AfterEach
    void tearDown() {
        if (server != null) {
            server.stop();
        }
    }

    @Test
    void testConnectionTimeout() throws Exception {
        // This test attempts to connect to a non-routable address, which should time out.
        String url = "http://************/index.html";
        String outputDir = tempDir.resolve("output-conn-timeout").toString();
        // Set a very short timeout for the test
        String[] args = {"--url", url, "--output", outputDir, "--timeout", "1"};
        CrawlerMain crawler = new CrawlerMain(args);
        try {
            crawler.start();
            // The crawler should fail gracefully without creating the output directory for the failed URL.
            assertFalse(Files.exists(Path.of(outputDir, "************")), "Output directory for unreachable host should not be created.");
        } finally {
            crawler.shutdown();
        }
    }

    @Test
    void testReadTimeout() throws Exception {
        // Stop the main server to free up the port for the slow server
        server.stop();

        // This test requires a server that accepts a connection but never sends data.
        // We can simulate this by connecting to a simple socket server that does nothing.
        try (java.net.ServerSocket slowServer = new java.net.ServerSocket(server.getPort())) {
            String url = "http://127.0.0.1:" + server.getPort() + "/index.html";
            String outputDir = tempDir.resolve("output-read-timeout").toString();
            String[] args = {"--url", url, "--output", outputDir, "--timeout", "1000"};
            CrawlerMain crawler = new CrawlerMain(args);
            try {
                crawler.start();
                assertFalse(Files.exists(Path.of(outputDir, "127.0.0.1", String.valueOf(server.getPort()))), "Output directory for slow host should not be created.");
            } finally {
                crawler.shutdown();
            }
        }
    }

    @Test
    void testDnsFailure() throws Exception {
        String url = "http://non-existent-domain-for-testing.com/index.html";
        String outputDir = tempDir.resolve("output-dns-failure").toString();
        String[] args = {"--url", url, "--output", outputDir};
        CrawlerMain crawler = new CrawlerMain(args);
        try {
            crawler.start();
            assertFalse(Files.exists(Path.of(outputDir, "non-existent-domain-for-testing.com")), "Output directory for non-existent domain should not be created.");
        } finally {
            crawler.shutdown();
        }
    }

    @Test
    void testLargeFileDownload() throws Exception {
        Path largeFilePath = tempDir.resolve(TEST_SITE_DIR).resolve("largefile.dat");
        // Create a 2MB file
        byte[] largeFileContent = new byte[2 * 1024 * 1024];
        Files.write(largeFilePath, largeFileContent);

        Files.writeString(tempDir.resolve(TEST_SITE_DIR).resolve("download.html"), "<html><body><a href='largefile.dat'>Download</a></body></html>");

        String url = "http://127.0.0.1:" + server.getPort() + "/" + TEST_SITE_DIR + "/download.html";
        String outputDir = tempDir.resolve("output-large-download").toString();
        String[] args = {"--url", url, "--output", outputDir, "--download-attachments", "true"};
        CrawlerMain crawler = new CrawlerMain(args);
        try {
            crawler.start();
            Thread.sleep(100); // Wait for file to be written
            // The file should be in the attachments directory with the actual port
            Path downloadedFile = Path.of(outputDir, "127.0.0.1:" + server.getPort(), TEST_SITE_DIR, "largefile.dat");
            assertTrue(Files.exists(downloadedFile), "Large file should be downloaded.");
            assertTrue(Files.size(downloadedFile) == largeFileContent.length, "Downloaded file size should match original.");
        } finally {
            crawler.shutdown();
        }
    }
}
