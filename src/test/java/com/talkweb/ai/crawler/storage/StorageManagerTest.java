package com.talkweb.ai.crawler.storage;

import com.talkweb.ai.crawler.model.PageData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StorageManager 类的单元测试。
 */
public class StorageManagerTest {

    private StorageManager storageManager;

    @TempDir
    Path tempDir;

    @BeforeEach
    public void setUp() {
        storageManager = new StorageManager(tempDir.toString());
    }

    @Test
    public void testSavePage() throws IOException {
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/test.html");
        pageData.setTitle("Test Page");
        pageData.setHtmlContent("title: Test Page\n# Test Content\nurl: http://example.com/test\n## 外部链接\n- http://external.com/link1\n- http://external.com/link2");
        pageData.setFetchTimestamp(LocalDateTime.now());
        pageData.setMetaData(new HashMap<>());
        pageData.setInternalLinks(Collections.emptyList());
        pageData.setExternalLinks(Arrays.asList("http://external.com/link1", "http://external.com/link2"));
        pageData.setAttachments(Collections.emptyList());

        Path savedPath = storageManager.savePage(pageData);

        assertTrue(Files.exists(savedPath));
        String content = new String(Files.readAllBytes(savedPath));
        assertTrue(content.contains("url: http://example.com/test"));
        assertTrue(content.contains("title: Test Page"));
        assertTrue(content.contains("# Test Content"));
        assertTrue(content.contains("## 外部链接"));
        assertTrue(content.contains("- http://external.com/link1"));
        assertTrue(content.contains("- http://external.com/link2"));
    }

    @Test
    public void testSavePageWithSpecialCharactersInUrl() throws IOException {
        // 测试URL中包含特殊字符的情况
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/path with spaces?q=特殊字符&lang=中文");
        pageData.setTitle("Special Characters Test");
        pageData.setMarkdownContent("# Content with special chars: 特殊字符 测试");
        pageData.setFetchTimestamp(LocalDateTime.now());
        pageData.setMetaData(new HashMap<>());

        assertThrows(IllegalArgumentException.class, () -> {
            // 存储管理器应处理特殊字符并抛出异常
            storageManager.savePage(pageData);
        });
    }

    @Test
    public void testSavePageHierarchicalStructure() throws IOException {
        // 测试具有层次结构的URL
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/category/subcategory/product.html");
        pageData.setTitle("Hierarchical Page");
        pageData.setHtmlContent("# Hierarchical content");
        pageData.setFetchTimestamp(LocalDateTime.now());

        Path savedPath = storageManager.savePage(pageData);

        assertTrue(Files.exists(savedPath));
        // 验证目录结构是否正确创建
        assertTrue(savedPath.toString().contains("category"));
        assertTrue(savedPath.toString().contains("subcategory"));
        assertTrue(Files.exists(savedPath.getParent()));
        assertTrue(Files.exists(savedPath.getParent().getParent()));
    }

    @Test
    public void testSaveMultiplePagesFromSameDomain() throws IOException {
        // 测试保存来自同一域名的多个页面
        String domain = "example.com";
        String[] paths = {"/page1.html", "/page2.html", "/dir/page3.html"};

        for (String path : paths) {
            PageData pageData = new PageData();
            pageData.setUrl("http://" + domain + path);
            pageData.setTitle("Page " + path);
            pageData.setHtmlContent("# Content of " + path);
            pageData.setFetchTimestamp(LocalDateTime.now());

            Path savedPath = storageManager.savePage(pageData);
            assertTrue(Files.exists(savedPath));
        }

        // 验证域名目录下文件数量
        Path domainDir = tempDir.resolve(domain);
        assertTrue(Files.exists(domainDir));

        // 验证所有文件都被保存到正确位置
        assertTrue(Files.exists(domainDir.resolve("page1.html")));
        assertTrue(Files.exists(domainDir.resolve("page2.html")));
        assertTrue(Files.exists(domainDir.resolve("dir").resolve("page3.html")));
    }

    @Test
    public void testConcurrentPageSaving() throws InterruptedException {
        // 测试并发保存多个页面
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            executor.submit(() -> {
                try {
                    PageData pageData = new PageData();
                    pageData.setUrl("http://example.com/concurrent/page" + index+".html");
                    pageData.setTitle("Concurrent Page " + index);
                    pageData.setHtmlContent("# Content of concurrent page " + index);
                    pageData.setFetchTimestamp(LocalDateTime.now());

                    Path savedPath = storageManager.savePage(pageData);

                    if (Files.exists(savedPath)) {
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成或超时
        assertTrue(latch.await(30, TimeUnit.SECONDS));
        executor.shutdown();

        // 验证所有页面都被成功保存
        assertEquals(threadCount, successCount.get());
    }

    @Test
    public void testSavePageWithLargeContent() throws IOException {
        // 测试保存大型内容
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/large.html");
        pageData.setTitle("Large Content Page");

        // 生成约10MB的内容
        StringBuilder largeContent = new StringBuilder("# Large Content Test\n\n");
        for (int i = 0; i < 1000000; i++) {
            largeContent.append("Line ").append(i).append(" of large content test\n");
        }
        pageData.setHtmlContent(largeContent.toString());
        pageData.setFetchTimestamp(LocalDateTime.now());

        Path savedPath = storageManager.savePage(pageData);

        assertTrue(Files.exists(savedPath));
        assertTrue(Files.size(savedPath) > 1000000); // 文件应该很大
    }

    @Test
    public void testSavePageWithEmptyContent() throws IOException {
        // 测试保存空内容
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/empty.html");
        pageData.setTitle("Empty Content Page");
        pageData.setHtmlContent("");  // 空内容
        pageData.setFetchTimestamp(LocalDateTime.now());

        Path savedPath = storageManager.savePage(pageData);

        assertTrue(Files.exists(savedPath));
        assertTrue(0< Files.size(Path.of(savedPath.toString()+".meta")), "meta文件应该存在");
        assertEquals(0, Files.size(savedPath));
    }

    @Test
    public void testSavePageWithInvalidOutputDir() {
        // 测试输出目录无效的情况
        StorageManager invalidStorageManager = new StorageManager("/non/existent/directory");

        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/invalid");
        pageData.setTitle("Invalid Dir Test");
        pageData.setMarkdownContent("# Invalid dir content");
        pageData.setFetchTimestamp(LocalDateTime.now());

        // 应该抛出异常或返回错误，但不应崩溃
        Exception exception = assertThrows(IOException.class,
            () -> invalidStorageManager.savePage(pageData));

        assertNotNull(exception.getMessage());
    }

    @Test
    public void testSavePageWithMetadata() throws IOException {
        // 测试保存带有元数据的页面
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/metadata.html");
        pageData.setTitle("Metadata Test Page");
        pageData.setHtmlContent("# Metadata Test Content");
        pageData.setFetchTimestamp(LocalDateTime.now());

        // 添加元数据
        Map<String, String> metadata = new HashMap<>();
        metadata.put("author", "Test Author");
        metadata.put("keywords", "test,metadata,storage");
        metadata.put("description", "A test page with metadata");
        pageData.setMetaData(metadata);

        Path savedPath = storageManager.savePage(pageData);
        Path metaPath = Path.of(savedPath.toString() + ".meta");
        assertTrue(Files.exists(metaPath));

        String content = new String(Files.readAllBytes(metaPath));

        // 验证元数据是否被保存在文件中
        assertTrue(content.contains("author: Test Author"));
        assertTrue(content.contains("keywords: test,metadata,storage"));
        assertTrue(content.contains("description: A test page with metadata"));
    }

    @Test
    public void testSavePageWithAttachments() throws IOException {
        // 测试带有附件的页面保存
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/attachments.html");
        pageData.setTitle("Attachments Test Page");
        pageData.setHtmlContent("# Page with attachments");
        pageData.setFetchTimestamp(LocalDateTime.now());

        // 添加附件
//        Map<String, byte[]> attachments = new HashMap<>();
//        attachments.put("image.jpg", "FAKE_IMAGE_DATA".getBytes());
//        attachments.put("document.pdf", "FAKE_PDF_DATA".getBytes());
//        pageData.setAttachments(attachments);

        Path savedPath = storageManager.savePage(pageData);

        assertTrue(Files.exists(savedPath));
    }
}
