package com.talkweb.ai.crawler.e2e;

import com.talkweb.ai.crawler.core.CrawlerMain;
import com.talkweb.ai.crawler.util.UrlUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 爬虫系统的端到端测试，验证整个系统的功能。
 */
@Testcontainers
public class CrawlerEndToEndTest {

    @TempDir
    Path tempDir;

    @Container
    private GenericContainer<?> webServer = new GenericContainer<>(DockerImageName.parse("nginx:alpine"))
            .withExposedPorts(80)
            .withClasspathResourceMapping("test-web-content", "/usr/share/nginx/html", org.testcontainers.containers.BindMode.READ_ONLY);

    @Test
    public void testCrawlerEndToEnd() throws IOException {
        String testUrl = "http://localhost:" + webServer.getFirstMappedPort() + "/index.html";
        String outputDir = tempDir.toString();

        // 设置命令行参数
        String[] args = new String[]{
            "--url", testUrl,
            "--output", outputDir,
            "--max-depth", "1",
            "--threads", "2",
            "--allowed-domains", "localhost"
        };

        // 启动爬虫
        CrawlerMain crawler = new CrawlerMain(args);
        crawler.start();
        // 等待爬虫完成
        try {
            Thread.sleep    (5000); // 等待5秒，确保爬虫有足够时间完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        crawler.shutdown();

        // 验证输出文件是否存在
        Path savedFilePath = Path.of(UrlUtils.generateLocalPath(outputDir, testUrl, null));
        assertTrue(Files.exists(savedFilePath), "Markdown file should be saved at expected path:"+ savedFilePath);

        // 验证输出文件内容
        String savedContent = new String(Files.readAllBytes(savedFilePath));
        assertTrue(savedContent.contains("Test Page1"), "Saved content should contain the URL");
     //   assertTrue(savedContent.contains("title: "), "Saved content should contain a title");
    }
}
