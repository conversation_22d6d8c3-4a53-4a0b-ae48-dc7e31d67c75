package com.talkweb.ai.crawler.converter;

import com.talkweb.ai.crawler.converter.HtmlToMarkdownConverter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HtmlToMarkdownConverter 类的单元测试。
 */
public class HtmlToMarkdownConverterTest {

    private HtmlToMarkdownConverter converter;

    @BeforeEach
    public void setUp() {
        converter = new HtmlToMarkdownConverter();
    }

    @Test
    public void testConvertWithTitle() {
        String html = "<html><head><title>Test Title</title></head><body><p>Content</p></body></html>";
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("# Test Title"));
        assertTrue(result.contains("Content"));
    }

    @Test
    public void testConvertHeadings() {
        String html = """
            <html><body>
            <h1>Heading 1</h1>
            <h2>Heading 2</h2>
            <h3>Heading 3</h3>
            <h4>Heading 4</h4>
            <h5>Heading 5</h5>
            <h6>Heading 6</h6>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("# Heading 1"));
        assertTrue(result.contains("## Heading 2"));
        assertTrue(result.contains("### Heading 3"));
        assertTrue(result.contains("#### Heading 4"));
        assertTrue(result.contains("##### Heading 5"));
        assertTrue(result.contains("###### Heading 6"));
    }

    @Test
    public void testConvertParagraphs() {
        String html = """
            <html><body>
            <p>First paragraph</p>
            <p>Second paragraph with some text</p>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("First paragraph"));
        assertTrue(result.contains("Second paragraph with some text"));
    }

    @Test
    public void testConvertUnorderedList() {
        String html = """
            <html><body>
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
                <li>Item 3</li>
            </ul>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("- Item 1"));
        assertTrue(result.contains("- Item 2"));
        assertTrue(result.contains("- Item 3"));
    }

    @Test
    public void testConvertOrderedList() {
        String html = """
            <html><body>
            <ol>
                <li>First item</li>
                <li>Second item</li>
                <li>Third item</li>
            </ol>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("1. First item"));
        assertTrue(result.contains("2. Second item"));
        assertTrue(result.contains("3. Third item"));
    }

    @Test
    public void testConvertBlockquote() {
        String html = "<html><body><blockquote>This is a quote</blockquote></body></html>";
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("> This is a quote"));
    }

    @Test
    public void testConvertCodeBlocks() {
        String html = """
            <html><body>
            <pre>function test() {
                return true;
            }</pre>
            <code>inline code</code>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("```"));
        assertTrue(result.contains("function test()"));
        assertTrue(result.contains("return true;"));
    }

    @Test
    public void testConvertImages() {
        String html = """
            <html><body>
            <img src="image1.jpg" alt="Image 1" title="Image Title">
            <img src="image2.png" alt="Image 2">
            <img src="image3.gif">
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("![Image 1](image1.jpg)"));
        assertTrue(result.contains("![Image 2](image2.png)"));
        assertTrue(result.contains("![](image3.gif)"));
    }

    @Test
    public void testConvertTable() {
        String html = """
            <html><body>
            <table>
                <tr>
                    <th>Header 1</th>
                    <th>Header 2</th>
                </tr>
                <tr>
                    <td>Cell 1</td>
                    <td>Cell 2</td>
                </tr>
                <tr>
                    <td>Cell 3</td>
                    <td>Cell 4</td>
                </tr>
            </table>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("| Header 1 | Header 2 |"));
        assertTrue(result.contains("| --- | --- |"));
        assertTrue(result.contains("| Cell 1 | Cell 2 |"));
        assertTrue(result.contains("| Cell 3 | Cell 4 |"));
    }

    @Test
    public void testConvertTableWithOnlyDataCells() {
        String html = """
            <html><body>
            <table>
                <tr>
                    <td>Data 1</td>
                    <td>Data 2</td>
                </tr>
                <tr>
                    <td>Data 3</td>
                    <td>Data 4</td>
                </tr>
            </table>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("| Data 1 | Data 2 |"));
        assertTrue(result.contains("| --- | --- |"));
        assertTrue(result.contains("| Data 3 | Data 4 |"));
    }

    @Test
    public void testConvertEmptyTable() {
        String html = "<html><body><table></table></body></html>";
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        // 空表格应该不产生任何markdown内容
        assertNotNull(result);
    }

    @Test
    public void testConvertComplexStructure() {
        String html = """
            <html>
            <head><title>Complex Document</title></head>
            <body>
                <h1>Main Title</h1>
                <p>Introduction paragraph</p>
                <h2>Section 1</h2>
                <ul>
                    <li>List item 1</li>
                    <li>List item 2</li>
                </ul>
                <blockquote>Important quote</blockquote>
                <table>
                    <tr><th>Name</th><th>Value</th></tr>
                    <tr><td>Item 1</td><td>100</td></tr>
                </table>
                <img src="chart.png" alt="Chart">
            </body>
            </html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("# Complex Document"));
        assertTrue(result.contains("# Main Title"));
        assertTrue(result.contains("## Section 1"));
        assertTrue(result.contains("- List item 1"));
        assertTrue(result.contains("> Important quote"));
        assertTrue(result.contains("| Name | Value |"));
        assertTrue(result.contains("![Chart](chart.png)"));
    }

    @Test
    public void testConvertNestedElements() {
        String html = """
            <html><body>
            <div>
                <h2>Section in Div</h2>
                <p>Paragraph in div</p>
                <span>Span content</span>
            </div>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("## Section in Div"));
        assertTrue(result.contains("Paragraph in div"));
        assertTrue(result.contains("Span content"));
    }

    @Test
    public void testConvertWithSpecialCharacters() {
        String html = """
            <html><body>
            <p>Text with &amp; ampersand, &lt; less than, &gt; greater than.</p>
            <p>Unicode: © ® ™</p>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("& ampersand"));
        assertTrue(result.contains("< less than"));
        assertTrue(result.contains("> greater than"));
        assertTrue(result.contains("©"));
        assertTrue(result.contains("®"));
        assertTrue(result.contains("™"));
    }

    @ParameterizedTest
    @CsvSource({
        "'<p>Simple paragraph</p>', 'Simple paragraph'",
        "'<div>Div content</div>', 'Div content'",
        "'<span>Span content</span>', 'Span content'",
        "'<strong>Bold text</strong>', 'Bold text'",
        "'<em>Italic text</em>', 'Italic text'"
    })
    public void testConvertBasicElements(String htmlFragment, String expectedText) {
        String fullHtml = "<html><body>" + htmlFragment + "</body></html>";
        Document doc = Jsoup.parse(fullHtml);
        
        String result = converter.convert(doc);
        
        assertTrue(result.contains(expectedText));
    }

    @Test
    public void testConvertEmptyDocument() {
        String html = "<html></html>";
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertNotNull(result);
        // 空文档应该返回空字符串或只包含换行符
    }

    @Test
    public void testConvertDocumentWithOnlyTitle() {
        String html = "<html><head><title>Only Title</title></head></html>";
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("# Only Title"));
    }

    @Test
    public void testConvertDocumentWithOnlyBody() {
        String html = "<html><body><p>Only body content</p></body></html>";
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("Only body content"));
    }

    @Test
    public void testConvertWithIgnoredElements() {
        String html = """
            <html><body>
            <p>Visible content</p>
            <script>alert('This should be ignored');</script>
            <style>body { color: red; }</style>
            <p>More visible content</p>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        assertTrue(result.contains("Visible content"));
        assertTrue(result.contains("More visible content"));
        // Script和style内容应该被忽略
        assertFalse(result.contains("alert"));
        assertFalse(result.contains("color: red"));
    }

    @Test
    public void testConvertPerformance() {
        StringBuilder htmlBuilder = new StringBuilder("<html><body>");
        for (int i = 0; i < 1000; i++) {
            htmlBuilder.append("<p>Paragraph ").append(i).append("</p>");
            htmlBuilder.append("<h2>Heading ").append(i).append("</h2>");
        }
        htmlBuilder.append("</body></html>");
        
        Document doc = Jsoup.parse(htmlBuilder.toString());

        long startTime = System.currentTimeMillis();
        String result = converter.convert(doc);
        long endTime = System.currentTimeMillis();

        assertNotNull(result);
        assertTrue(endTime - startTime < 3000, "Conversion should complete within 3 seconds");
        assertTrue(result.contains("Paragraph 0"));
        assertTrue(result.contains("Paragraph 999"));
        assertTrue(result.contains("## Heading 0"));
        assertTrue(result.contains("## Heading 999"));
    }

    @Test
    public void testConvertWithMalformedTable() {
        String html = """
            <html><body>
            <table>
                <tr>
                    <td>Cell without header</td>
                </tr>
                <tr>
                    <td>Another cell</td>
                    <td>Extra cell</td>
                </tr>
            </table>
            </body></html>
            """;
        Document doc = Jsoup.parse(html);

        String result = converter.convert(doc);

        // 应该能处理不规则的表格
        assertTrue(result.contains("| Cell without header |"));
        assertTrue(result.contains("| Another cell | Extra cell |"));
    }
}