package com.talkweb.ai.crawler.service;

import com.talkweb.ai.crawler.coordinator.CrawlCoordinator;
import com.talkweb.ai.crawler.dto.CreateTaskRequest;
import com.talkweb.ai.crawler.dto.TaskDto;
import com.talkweb.ai.crawler.mapper.TaskMapper;
import com.talkweb.ai.crawler.model.Task;
import com.talkweb.ai.crawler.model.TaskStatus;
import com.talkweb.ai.crawler.repository.TaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskServiceImplTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private CrawlCoordinator crawlCoordinator;

    @InjectMocks
    private TaskServiceImpl taskService;

    private Task task;
    private TaskDto taskDto;
    private CreateTaskRequest request;

    @BeforeEach
    void setUp() {
        task = new Task();
        task.setId(UUID.randomUUID());
        task.setUrl("https://example.com");
        task.setStatus(TaskStatus.PENDING);
        task.setStartTime(LocalDateTime.now());

        taskDto = new TaskDto();
        taskDto.setTaskId(task.getId());
        taskDto.setUrl(task.getUrl());
        taskDto.setStatus(task.getStatus());

        request = new CreateTaskRequest();
        request.setUrl("https://example.com");
        request.setMaxDepth(3);
    }

    @Test
    void createTask_ShouldReturnTaskDto() {
        when(taskMapper.toEntity(any(CreateTaskRequest.class))).thenReturn(task);
        when(taskRepository.save(any(Task.class))).thenReturn(task);
        when(taskMapper.toDto(any(Task.class))).thenReturn(taskDto);

        TaskDto result = taskService.createTask(request);

        assertNotNull(result);
        assertEquals(taskDto.getTaskId(), result.getTaskId());
        verify(crawlCoordinator, times(1)).startCrawlAsync(any(), any());
    }

    @Test
    void getTaskById_ShouldReturnTaskDto() {
        UUID taskId = task.getId();
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));
        when(taskMapper.toDto(task)).thenReturn(taskDto);

        Optional<TaskDto> result = taskService.getTaskById(taskId);

        assertTrue(result.isPresent());
        assertEquals(taskDto.getTaskId(), result.get().getTaskId());
    }

    @Test
    void cancelTask_ShouldUpdateStatus() {
        task.setStatus(TaskStatus.RUNNING);
        when(taskRepository.findById(task.getId())).thenReturn(Optional.of(task));
        when(taskRepository.save(task)).thenReturn(task);
        when(taskMapper.toDto(task)).thenReturn(taskDto);

        Optional<TaskDto> result = taskService.cancelTask(task.getId());

        assertTrue(result.isPresent());
        assertEquals(TaskStatus.CANCELLED, task.getStatus());
        assertNotNull(task.getEndTime());
    }
}