package com.talkweb.ai.crawler.config;

import com.talkweb.ai.crawler.config.ConfigManager;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConfigManager 类的单元测试。
 */
public class ConfigManagerTest {

    @TempDir
    Path tempDir;

    @Test
    public void testDefaultConfiguration() {
        String[] args = {};
        ConfigManager configManager = new ConfigManager(args, null);
        
        assertEquals("", configManager.getUrl());
        assertEquals(3, configManager.getMaxDepth());
        assertEquals(4, configManager.getThreads());
        assertEquals("./output", configManager.getOutputDir());
        assertFalse(configManager.isPrometheusEnabled());
        assertArrayEquals(new String[0], configManager.getAllowedDomains());
    }

    @Test
    public void testCommandLineArguments() {
        String[] args = {
            "--url", "http://test.com",
            "--max-depth", "5",
            "--threads", "8",
            "--output", "/tmp/test",
            "--allowed-domains", "test.com,api.test.com",
            "--enable-prometheus", "true"
        };
        
        ConfigManager configManager = new ConfigManager(args, null);
        
        assertEquals("http://test.com", configManager.getUrl());
        assertEquals(5, configManager.getMaxDepth());
        assertEquals(8, configManager.getThreads());
        assertEquals("/tmp/test", configManager.getOutputDir());
        assertTrue(configManager.isPrometheusEnabled());
        assertArrayEquals(new String[]{"test.com", "api.test.com"}, configManager.getAllowedDomains());
    }

    @Test
    public void testConfigFileLoading() throws IOException {
        // 创建测试配置文件
        Path configFile = tempDir.resolve("test.properties");
        Properties props = new Properties();
        props.setProperty("url", "http://example.com");
        props.setProperty("maxDepth", "7");
        props.setProperty("threads", "12");
        props.setProperty("output", "/test/output");
        props.setProperty("allowedDomains", "example.com,sub.example.com");
        props.setProperty("enablePrometheus", "true");
        
        try (var writer = Files.newBufferedWriter(configFile)) {
            props.store(writer, "Test configuration");
        }
        
        String[] args = {};
        ConfigManager configManager = new ConfigManager(args, configFile.toString());
        
        assertEquals("http://example.com", configManager.getUrl());
        assertEquals(7, configManager.getMaxDepth());
        assertEquals(12, configManager.getThreads());
        assertEquals("/test/output", configManager.getOutputDir());
        assertTrue(configManager.isPrometheusEnabled());
        assertArrayEquals(new String[]{"example.com", "sub.example.com"}, configManager.getAllowedDomains());
    }

    @Test
    public void testCommandLineOverridesConfigFile() throws IOException {
        // 创建配置文件
        Path configFile = tempDir.resolve("test.properties");
        Properties props = new Properties();
        props.setProperty("url", "http://file.com");
        props.setProperty("maxDepth", "3");
        props.setProperty("threads", "4");
        props.setProperty("output", "/file/output");
        
        try (var writer = Files.newBufferedWriter(configFile)) {
            props.store(writer, "Test configuration");
        }
        
        // 命令行参数覆盖部分配置文件值
        String[] args = {
            "--url", "http://override.com",
            "--max-depth", "10"
            // threads 应该来自配置文件
        };
        
        ConfigManager configManager = new ConfigManager(args, configFile.toString());
        
        assertEquals("http://override.com", configManager.getUrl()); // 来自CLI
        assertEquals(10, configManager.getMaxDepth()); // 来自CLI
        assertEquals(4, configManager.getThreads()); // 来自配置文件
        assertEquals("/file/output", configManager.getOutputDir()); // 来自配置文件
    }

    @Test
    public void testNonExistentConfigFile() {
        String[] args = {};
        String nonExistentFile = "/nonexistent/config.properties";
        
        // 应该优雅地处理不存在的配置文件
        assertDoesNotThrow(() -> {
            ConfigManager configManager = new ConfigManager(args, nonExistentFile);
            // 应该使用默认值
            assertEquals(3, configManager.getMaxDepth());
            assertEquals(4, configManager.getThreads());
        });
    }

    @Test
    public void testMalformedConfigFile() throws IOException {
        // 创建格式错误的配置文件
        Path configFile = tempDir.resolve("malformed.properties");
        Files.writeString(configFile, "invalid content\nno equals sign\n");
        
        String[] args = {};
        
        // 应该优雅地处理格式错误的配置文件
        assertDoesNotThrow(() -> {
            ConfigManager configManager = new ConfigManager(args, configFile.toString());
            // 应该使用默认值
            assertEquals(3, configManager.getMaxDepth());
        });
    }

    @Test
    public void testInvalidNumericValues() {
        String[] args = {
            "--max-depth", "invalid",
            "--threads", "not-a-number"
        };
        
        ConfigManager configManager = new ConfigManager(args, null);
        
        // 无效的数值应该回退到默认值
        assertEquals(3, configManager.getMaxDepth()); // 默认值
        assertEquals(4, configManager.getThreads()); // 默认值
    }

    @Test
    public void testEmptyAllowedDomains() {
        String[] args = {
            "--allowed-domains", ""
        };
        
        ConfigManager configManager = new ConfigManager(args, null);
        
        assertArrayEquals(new String[0], configManager.getAllowedDomains());
    }

    @Test
    public void testAllowedDomainsWithSpaces() {
        String[] args = {
            "--allowed-domains", " domain1.com , domain2.com , domain3.com "
        };
        
        ConfigManager configManager = new ConfigManager(args, null);
        
        String[] domains = configManager.getAllowedDomains();
        assertEquals(3, domains.length);
        assertEquals(" domain1.com ", domains[0]); // 实际实现不会trim空格
        assertEquals(" domain2.com ", domains[1]);
        assertEquals(" domain3.com ", domains[2]);
    }

    @Test
    public void testPrometheusEnabledVariations() {
        // 测试不同的prometheus启用值
        String[][] testCases = {
            {"true", "true"},
            {"TRUE", "true"},
            {"True", "true"},
            {"1", "true"},
            {"false", "false"},
            {"FALSE", "false"},
            {"0", "false"},
            {"invalid", "false"}
        };
        
        for (String[] testCase : testCases) {
            String[] args = {"--enable-prometheus", testCase[0]};
            ConfigManager configManager = new ConfigManager(args, null);
            
            boolean expected = "true".equals(testCase[1]);
            assertEquals(expected, configManager.isPrometheusEnabled(), 
                "Failed for input: " + testCase[0]);
        }
    }

    @Test
    public void testUnknownCommandLineArguments() {
        String[] args = {
            "--url", "http://test.com",
            "--unknown-arg", "value",
            "--another-unknown", "another-value",
            "--threads", "8"
        };
        
        // 应该忽略未知参数，不抛出异常
        assertDoesNotThrow(() -> {
            ConfigManager configManager = new ConfigManager(args, null);
            assertEquals("http://test.com", configManager.getUrl());
            assertEquals(8, configManager.getThreads());
        });
    }

    @Test
    public void testMissingArgumentValues() {
        String[] args = {
            "--url", // 缺少值
            "--threads", "8"
        };
        
        ConfigManager configManager = new ConfigManager(args, null);
        
        // 缺少值的参数会把下一个参数当作值
        assertEquals("--threads", configManager.getUrl()); // 实际行为
        assertEquals(4, configManager.getThreads()); // 默认值，因为"8"被当作URL的值
    }

    @Test
    public void testConfigFileWithInlineConfig() throws IOException {
        // 创建配置文件
        Path configFile = tempDir.resolve("base.properties");
        Properties props = new Properties();
        props.setProperty("url", "http://base.com");
        props.setProperty("maxDepth", "5");
        
        try (var writer = Files.newBufferedWriter(configFile)) {
            props.store(writer, "Base configuration");
        }
        
        // 创建另一个配置文件
        Path overrideConfigFile = tempDir.resolve("override.properties");
        Properties overrideProps = new Properties();
        overrideProps.setProperty("url", "http://override.com");
        overrideProps.setProperty("threads", "16");
        
        try (var writer = Files.newBufferedWriter(overrideConfigFile)) {
            overrideProps.store(writer, "Override configuration");
        }
        
        String[] args = {
            "--config", overrideConfigFile.toString()
        };
        
        ConfigManager configManager = new ConfigManager(args, configFile.toString());
        
        // 应该使用通过--config指定的文件，但base文件的maxDepth会被保留
        assertEquals("http://override.com", configManager.getUrl());
        assertEquals(16, configManager.getThreads());
        assertEquals(5, configManager.getMaxDepth()); // 来自base文件
    }

    @Test
    public void testPrintConfig() {
        String[] args = {
            "--url", "http://test.com",
            "--max-depth", "5",
            "--threads", "8"
        };
        
        ConfigManager configManager = new ConfigManager(args, null);
        
        // printConfig不应该抛出异常
        assertDoesNotThrow(() -> configManager.printConfig());
    }

    @Test
    public void testConfigWithSpecialCharacters() throws IOException {
        // 创建包含特殊字符的配置文件
        Path configFile = tempDir.resolve("special.properties");
        Properties props = new Properties();
        props.setProperty("url", "http://example.com/path with spaces");
        props.setProperty("output", "/path/with/english/characters");
        
        try (var writer = Files.newBufferedWriter(configFile)) {
            props.store(writer, "Special characters configuration");
        }
        
        String[] args = {};
        ConfigManager configManager = new ConfigManager(args, configFile.toString());
        
        assertEquals("http://example.com/path with spaces", configManager.getUrl());
        assertEquals("/path/with/english/characters", configManager.getOutputDir());
    }

    @Test
    public void testConfigManagerWithNullArgs() {
        // 测试null参数数组 - 实际会抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            new ConfigManager(null, null);
        });
    }

    @Test
    public void testConfigManagerWithEmptyArgs() {
        String[] emptyArgs = {};
        
        ConfigManager configManager = new ConfigManager(emptyArgs, null);
        
        assertEquals("", configManager.getUrl());
        assertEquals(3, configManager.getMaxDepth());
        assertEquals(4, configManager.getThreads());
        assertEquals("./output", configManager.getOutputDir());
        assertFalse(configManager.isPrometheusEnabled());
    }
}