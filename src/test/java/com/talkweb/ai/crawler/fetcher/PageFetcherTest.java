package com.talkweb.ai.crawler.fetcher;

import com.talkweb.ai.crawler.model.PageData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PageFetcher 类的单元测试。
 */
@ExtendWith(MockitoExtension.class)
public class PageFetcherTest {

    private final PageFetcher pageFetcher = new PageFetcher(10000, 5000, 1);

    @Mock
    private PageFetcher mockPageFetcher;

    @Test
    public void testFetch_Successful() throws IOException {
        String url = "https://example.com";
        PageData page = pageFetcher.fetchPageContent(url);
        assertNotNull(page);
        String content = page.getHtmlContent();
        assertNotNull(content);
        assertFalse(content.isEmpty());
    }

    @Test
    public void testFetch_Failure() {
        String url = "https://nonexistent.domain.tld";
        assertThrows(IOException.class, () -> pageFetcher.fetchPageContent(url));
    }

    @Test
    public void testRetryMechanism() throws IOException {
        // 创建一个使用3次重试的PageFetcher
        PageFetcher retryPageFetcher = new PageFetcher(5000, 5000, 3);

        // 使用代理对象模拟失败情况
        PageFetcher spyFetcher = Mockito.spy(retryPageFetcher);
        int MAX_RETRIES = 3;
        spyFetcher.setMaxRetries(MAX_RETRIES);
        final AtomicInteger attemptCount = new AtomicInteger(0);
        when(spyFetcher.fetchPageContentNoRetry("https://example.com")).thenAnswer(invocation -> {
                String url = invocation.getArgument(0);
                if (attemptCount.incrementAndGet() < MAX_RETRIES) {
                    throw new IOException("网络错误");
                }
                return new PageData("https://example.com", null, "<html><body>成功重试后的内容</body></html>");
            }
        );

        PageData result = spyFetcher.fetchPageContent("https://example.com");

        // 验证内容正确获取且尝试了3次
        assertNotNull(result);
        assertEquals("<html><body>成功重试后的内容</body></html>", result.getHtmlContent());
        verify(spyFetcher, times(3)).fetchPageContentNoRetry(anyString());
    }

    @Test
    public void testTimeoutHandling() {
        // 使用极短的超时时间创建PageFetcher
        PageFetcher timeoutPageFetcher = new PageFetcher(10, 10, 1);

        // 访问可能需要较长时间响应的网站
        String url = "https://httpbin.org/delay/2"; // 此网站会延迟2秒响应

        // 验证是否抛出超时异常
        Exception exception = assertThrows(IOException.class, () -> {
            timeoutPageFetcher.fetchPageContent(url);
        });

        assertTrue(exception.getMessage().contains("timeout") ||
            exception.getCause() instanceof SocketTimeoutException);
    }

    @Test
    public void testParallelFetching() throws InterruptedException {
        // 创建一个线程池执行并发请求
        ExecutorService executor = Executors.newFixedThreadPool(3);
        List<String> urls = Arrays.asList(
            "https://example.com",
            "https://httpbin.org/html",
            "https://httpbin.org/xml"
        );

        CountDownLatch latch = new CountDownLatch(urls.size());
        ConcurrentHashMap<String, PageData> results = new ConcurrentHashMap<>();

        for (String url : urls) {
            executor.submit(() -> {
                try {
                    PageData data = pageFetcher.fetchPageContent(url);
                    results.put(url, data);
                } catch (IOException e) {
                    // 忽略错误
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有请求完成或超时
        assertTrue(latch.await(30, TimeUnit.SECONDS));
        executor.shutdown();

        // 验证至少有部分URL成功获取内容
        assertFalse(results.isEmpty());
        for (PageData data : results.values()) {
            assertNotNull(data.getHtmlContent());
            assertFalse(data.getHtmlContent().isEmpty());
        }
    }

    @Test
    public void testExtractLinks() throws IOException {
        // 创建包含链接的HTML内容
        String html = "<html><body>" +
            "<a href='https://example.com/page1.html'>Link 1</a>" +
            "<a href='https://example.com/page2.html'>Link 2</a>" +
            "<a href='https://external.com/'>External Link</a>" +
            "</body></html>";

        // 模拟页面获取
        when(mockPageFetcher.fetchPageContent("https://example.com"))
            .thenReturn(new PageData("https://example.com", null, html));

        PageData page = mockPageFetcher.fetchPageContent("https://example.com");

        // 验证获取的内容正确
        assertEquals(html, page.getHtmlContent());
    }
}
