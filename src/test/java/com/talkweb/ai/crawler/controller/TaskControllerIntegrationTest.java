package com.talkweb.ai.crawler.controller;

import com.talkweb.ai.crawler.dto.CreateTaskRequest;
import com.talkweb.ai.crawler.dto.TaskDto;
import com.talkweb.ai.crawler.model.Task;
import com.talkweb.ai.crawler.model.TaskStatus;
import com.talkweb.ai.crawler.repository.TaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import com.talkweb.ai.crawler.metrics.MetricsExporter;
import com.talkweb.ai.crawler.coordinator.CrawlCoordinator;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
class TaskControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockitoBean
    private MetricsExporter metricsExporter;
    
    @MockitoBean
    private CrawlCoordinator crawlCoordinator;

    private Task testTask;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        taskRepository.deleteAll();

        testTask = new Task();
        testTask.setId(UUID.randomUUID());
        testTask.setUrl("https://example.com");
        testTask.setStatus(TaskStatus.PENDING);
        testTask.setStartTime(LocalDateTime.now());
        taskRepository.save(testTask);
    }

    @Test
    void createTask_ShouldReturnCreated() throws Exception {
        CreateTaskRequest request = new CreateTaskRequest();
        request.setUrl("https://test.com");
        request.setMaxDepth(2);

        mockMvc.perform(post("/api/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content("""
                    {
                        "url": "https://test.com",
                        "maxDepth": 2
                    }
                    """))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.taskId", notNullValue()))
                .andExpect(jsonPath("$.status", is("PENDING")));
    }

    @Test
    void getTaskById_ShouldReturnTask() throws Exception {
        mockMvc.perform(get("/api/tasks/{id}", testTask.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId", is(testTask.getId().toString())))
                .andExpect(jsonPath("$.url", is(testTask.getUrl())));
    }

    @Test
    void cancelTask_ShouldUpdateStatus() throws Exception {
        testTask.setStatus(TaskStatus.RUNNING);
        taskRepository.save(testTask);

        mockMvc.perform(post("/api/tasks/{id}/cancel", testTask.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("CANCELLED")));
    }

    @Test
    void getTasks_ShouldReturnPage() throws Exception {
        mockMvc.perform(get("/api/tasks"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThan(0))));
    }
}