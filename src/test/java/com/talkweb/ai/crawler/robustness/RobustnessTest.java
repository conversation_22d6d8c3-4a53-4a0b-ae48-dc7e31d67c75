package com.talkweb.ai.crawler.robustness;

import com.talkweb.ai.crawler.core.CrawlerMain;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.assertTrue;

public class RobustnessTest {

    private static final String TEST_SITE_DIR = "test-site-robustness";
    @TempDir
    Path tempDir;
    private SimpleWebServer server;

    @BeforeEach
    void setUp() throws IOException {
        // Create a test site with some challenging content
        Path sitePath = tempDir.resolve(TEST_SITE_DIR);
        Files.createDirectories(sitePath);

        // Page with a lot of invalid links
        Files.writeString(sitePath.resolve("invalid-links.html"),
                "<html><body>" +
                        "<a href='/non-existent.html'>Non-existent</a>" +
                        "<a href='http://127.0.0.1:9999/unreachable'>Unreachable</a>" +
                        "</body></html>");

        // Page with very large content
        StringBuilder largeContent = new StringBuilder("<html><body>");
        for (int i = 0; i < 10000; i++) {
            largeContent.append("<p>This is a very long line of text to test memory usage.</p>");
        }
        largeContent.append("</body></html>");
        Files.writeString(sitePath.resolve("large-page.html"), largeContent.toString());

        // Page that causes a redirect loop
        Files.writeString(sitePath.resolve("loop1.html"), "<html><head><meta http-equiv='refresh' content='0;url=/test-site-robustness/loop2.html'></head><body>Redirecting...</body></html>");
        Files.writeString(sitePath.resolve("loop2.html"), "<html><head><meta http-equiv='refresh' content='0;url=/test-site-robustness/loop1.html'></head><body>Redirecting...</body></html>");

        server = new SimpleWebServer(tempDir, 0);
        server.start();
    }

    @AfterEach
    void tearDown() {
        server.stop();
    }

    @Test
    void testCrawlerHandlesInvalidLinksGracefully() throws Exception {
        String url = "http://127.0.0.1:" + server.getPort() + "/" + TEST_SITE_DIR + "/invalid-links.html";
        String outputDir = tempDir.resolve("output-invalid-links").toString();
        String[] args = {"--url", url, "--output", outputDir, "--max-depth", "1"};
        CrawlerMain crawler = new CrawlerMain(args);
        try {
            crawler.start();
            Thread.sleep(100); // Wait for file to be written
            // Check that the crawler completes without crashing and creates the output directory.
            assertTrue(Files.exists(Path.of(outputDir)), "Output directory should be created.");
        } finally {
            crawler.shutdown();
        }
    }

    @Test
    void testCrawlerHandlesLargePagesWithoutCrashing() throws Exception {
        String url = "http://127.0.0.1:" + server.getPort() + "/" + TEST_SITE_DIR + "/large-page.html";
        String outputDir = tempDir.toString();
        String downloadDir =outputDir+"_out";

        String[] args = {"--url", url, "--output", downloadDir, "--max-depth", "1"};
        CrawlerMain crawler = new CrawlerMain(args);
        try {
            crawler.start();
            Thread.sleep(300); // Wait for file to be written
            Path mdFile = Path.of(downloadDir+"/127.0.0.1:"+server.getPort()+"/"+TEST_SITE_DIR+ "/large-page.html");
            assertTrue(Files.exists(mdFile), "Markdown file for large page should be created."+mdFile);
            assertTrue(Files.size(mdFile) > 1000, "Markdown file should have significant content.");
        } finally {
            crawler.shutdown();
        }
    }

    @Test
    void testCrawlerDetectsAndBreaksRedirectLoops() throws Exception {
        String url = "http://127.0.0.1:" + server.getPort() + "/" + TEST_SITE_DIR + "/loop1.html";
        String outputDir = tempDir.toString();
        String downloadDir =outputDir+"_out";
        String[] args = {"--url", url, "--output", downloadDir, "--max-depth", "5"}; // High depth to test loop detection
        CrawlerMain crawler = new CrawlerMain(args);
        try {
            crawler.start();
            Thread.sleep(300); // Wait for file to be written
            // The crawler should stop after a certain number of redirects and not get stuck.
            // We can check the log output (if available) or just ensure the process finishes.
            // For this test, we'll just assert that the output directory is created,
            // implying the crawler didn't hang indefinitely.
            Path mdFile = Path.of(downloadDir, "127.0.0.1:"+server.getPort(), TEST_SITE_DIR, "loop1.html");
            assertTrue(Files.exists(mdFile), "Output directory should be created even with redirect loops."+mdFile);
        } finally {
            crawler.shutdown();
        }
    }
}
