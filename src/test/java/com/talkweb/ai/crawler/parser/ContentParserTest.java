package com.talkweb.ai.crawler.parser;

import com.talkweb.ai.crawler.model.PageData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ContentParser 类的单元测试。
 */
public class ContentParserTest {

    private ContentParser contentParser;

    @TempDir
    Path tempDir;

    @BeforeEach
    public void setUp() {
        contentParser = new ContentParser(tempDir.toString());
    }

    @Test
    public void testParseBasicHtml() {
        String html = """
            <html>
            <head>
                <title>Test Page</title>
                <meta name="description" content="Test description">
                <meta name="keywords" content="test,page">
            </head>
            <body>
                <h1>Main Title</h1>
                <p>This is a test paragraph.</p>
                <a href="/internal">Internal Link</a>
                <a href="http://external.com">External Link</a>
            </body>
            </html>
            """;
        String url = "http://example.com/test";

        PageData result = contentParser.parse(html, url);

        // 验证基本属性
        assertEquals(url, result.getUrl());
        assertEquals("Test Page", result.getTitle());
        assertNotNull(result.getMetaData());
       // assertEquals("Test description", result.getMetaData().getDescription());
        //assertTrue(result.getMetaData().getKeywords().contains("test"));
       // assertTrue(result.getMetaData().getKeywords().contains("page"));

        // 验证链接提取
        assertTrue(result.getInternalLinks().contains("http://example.com/internal"));
        assertTrue(result.getExternalLinks().contains("http://external.com"));
    }

    @Test
    public void testParseComplexNestedHtml() {
        String html = """
            <html>
            <head>
                <title>Complex Page</title>
            </head>
            <body>
                <div class="container">
                    <div class="content">
                        <h1>Main Title</h1>
                        <div class="article">
                            <p>This is a <strong>complex</strong> nested structure.</p>
                            <ul>
                                <li>Item 1 with <a href="/link1">link</a></li>
                                <li>Item 2 with <a href="http://external1.com">external link</a></li>
                                <li>
                                    Nested list:
                                    <ol>
                                        <li>Nested item with <a href="/nested/link">nested link</a></li>
                                    </ol>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="sidebar">
                        <a href="/sidebar1">Sidebar link 1</a>
                        <a href="http://external2.com">Sidebar external link</a>
                    </div>
                </div>
            </body>
            </html>
            """;
        String url = "http://example.com/complex";

        PageData result = contentParser.parse(html, url);

        // 验证标题
        assertEquals("Complex Page", result.getTitle());

        // 验证所有内部链接都被正确提取
        List<String> expectedInternalLinks = List.of(
            "http://example.com/link1",
            "http://example.com/nested/link",
            "http://example.com/sidebar1"
        );
        for (String link : expectedInternalLinks) {
            assertTrue(result.getInternalLinks().contains(link),
                    "应该包含内部链接: " + link);
        }

        // 验证所有外部链接都被正确提取
        List<String> expectedExternalLinks = List.of(
            "http://external1.com",
            "http://external2.com"
        );
        for (String link : expectedExternalLinks) {
            assertTrue(result.getExternalLinks().contains(link),
                    "应该包含外部链接: " + link);
        }
    }

    @Test
    public void testParseWithRelativeLinks() {
        String html = """
            <html>
            <body>
                <a href="/">首页</a>
                <a href="../parent">上级目录</a>
                <a href="./current">当前目录</a>
                <a href="relative">相对路径</a>
                <a href="#section">页内锚点</a>
                <a href="?param=value">URL参数</a>
            </body>
            </html>
            """;
        String url = "http://example.com/dir/page.html";

        PageData result = contentParser.parse(html, url);

        // 验证相对路径被正确解析为绝对路径
        List<String> expectedLinks = List.of(
            "http://example.com/",                // 根路径
            "http://example.com/parent",          // 上级目录
            "http://example.com/dir/current",     // 当前目录
            "http://example.com/dir/relative",    // 相对路径
            // 页内锚点和URL参数应该被保留
            "http://example.com/dir/?param=value", // URL参数
            "http://example.com/dir/page.html#section"
        );

        for (String link : expectedLinks) {
            assertTrue(result.getInternalLinks().contains(link),
                    "应该包含解析后的内部链接: " + link);
        }
    }

    @Test
    public void testParseWithSpecialCharacters() {
        String html = """
            <html>
            <head>
                <title>Special Chars & Entities</title>
            </head>
            <body>
                <h1>Special &amp; Characters Test</h1>
                <p>Contains &lt;special&gt; &quot;characters&quot; &amp; entities.</p>
                <a href="/path with spaces">Link with spaces</a>
                <a href="/encoded%20path">Encoded path</a>
                <a href="/path?q=a&amp;b=c">Query parameters</a>
                <a href="/ünicode/path">Unicode path</a>
            </body>
            </html>
            """;
        String url = "http://example.com/special";

        PageData result = contentParser.parse(html, url);

        // 验证标题中的HTML实体被正确解码
        assertEquals("Special Chars & Entities", result.getTitle());

        // 验证特殊字符的链接被正确处理
        List<String> expectedLinks = List.of(
           // "http://example.com/path with spaces", //不符合规范的URL格式
            "http://example.com/encoded%20path",
            "http://example.com/path?q=a&b=c",
            "http://example.com/ünicode/path"
        );
        assertEquals(expectedLinks.size(), result.getInternalLinks().size(),
                "内部链接数量应该匹配预期");
        for (String link : expectedLinks) {
            assertTrue(result.getInternalLinks().contains(link),
                    "应该包含特殊字符链接: " + link);
        }
    }


    @Test
    public void testParseEmptyOrMalformedHtml() {
        // 测试空HTML
        PageData resultEmpty = contentParser.parse("", "http://example.com/empty");
        assertNotNull(resultEmpty);
        assertEquals("http://example.com/empty", resultEmpty.getUrl());
        assertTrue(resultEmpty.getInternalLinks().isEmpty());
        assertTrue(resultEmpty.getExternalLinks().isEmpty());

        // 测试畸形HTML - 没有结束标签
        String malformedHtml = "<html><body><p>Incomplete paragraph";
        PageData resultMalformed = contentParser.parse(malformedHtml, "http://example.com/malformed");
        assertNotNull(resultMalformed);
        assertEquals("http://example.com/malformed", resultMalformed.getUrl());
        // 即使HTML畸形，解析器也应该尽力提取可用内容
        assertNotNull(resultMalformed.getHtmlContent());
    }

    @Test
    public void testParseWithJavascriptLinks() {
        String html = """
            <html>
            <body>
                <a href="javascript:void(0)" onclick="goToPage('/dynamic1')">JS Link 1</a>
                <a href="#" onclick="window.location='/dynamic2'">JS Link 2</a>
                <button onclick="window.location.href='/dynamic3'">JS Button</button>
                <a href="javascript:alert('Not a real link')">JS Alert</a>
            </body>
            </html>
            """;
        String url = "http://example.com/js";

        PageData result = contentParser.parse(html, url);

        // 验证JavaScript链接的处理 - 这里需要根据ContentParser的实际实现来调整预期
        // 如果解析器能从onclick事件中提取URL，则应该包含这些链接
        List<String> potentialJsLinks = List.of(
            "http://example.com/dynamic1",
            "http://example.com/dynamic2",
            "http://example.com/dynamic3"
        );

        // 验证基本属性
        assertEquals(url, result.getUrl());

        // 这里只断言JavaScript链接不会导致解析错误，具体的提取行为取决于实际实现
        assertNotNull(result.getInternalLinks());
    }

    @Test
    public void testParseWithIFrames() {
        String html = """
            <html>
            <body>
                <iframe src="/frame1.html"></iframe>
                <iframe src="http://external.com/frame2.html"></iframe>
                <iframe src="about:blank"></iframe>
            </body>
            </html>
            """;
        String url = "http://example.com/iframe";

        PageData result = contentParser.parse(html, url);

        // 验证iframe源被正确提取
        assertTrue(result.getInternalLinks().contains("http://example.com/frame1.html"),
                "应该包含内部iframe源链接");
        assertTrue(result.getExternalLinks().contains("http://external.com/frame2.html"),
                "应该包含外部iframe源链接");
        // about:blank不应该被当作有效链接
        assertFalse(result.getInternalLinks().contains("about:blank"),
                "不应该包含about:blank链接");
    }

    @Test
    public void testParseWithBaseHref() {
        String html = """
            <html>
            <head>
                <base href="http://another-domain.com/base/">
            </head>
            <body>
                <a href="page1.html">Link 1</a>
                <a href="/absolute-path">Link 2</a>
                <a href="http://external.com">External Link</a>
            </body>
            </html>
            """;
        String url = "http://example.com/test";

        PageData result = contentParser.parse(html, url);

        // 验证相对链接是否根据base标签解析
        assertTrue(result.getExternalLinks().contains("http://another-domain.com/base/page1.html") ||
                   result.getInternalLinks().contains("http://another-domain.com/base/page1.html"),
                "相对链接应该基于base href解析");

        assertTrue(result.getExternalLinks().contains("http://another-domain.com/absolute-path") ||
                   result.getInternalLinks().contains("http://another-domain.com/absolute-path"),
                "绝对路径应该基于base href的域名");

        // 验证完全限定的URL不受base href影响
        assertTrue(result.getExternalLinks().contains("http://external.com"),
                "完全限定的URL不应该受base href影响");
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "<html><body><a href='https://example.com:443/secure'>HTTPS Link</a></body></html>",
        "<html><body><a href='http://example.com:80/normal'>HTTP Link</a></body></html>",
        "<html><body><a href='ftp://example.com/file.zip'>FTP Link</a></body></html>"
    })
    public void testParseWithDifferentProtocols(String html) {
        String url = "http://example.com/protocols";

        PageData result = contentParser.parse(html, url);

        // 验证不同协议的链接被正确提取
        if (html.contains("https://example.com:443")) {
            assertTrue(result.getExternalLinks().contains("https://example.com:443/secure") ||
                       result.getInternalLinks().contains("https://example.com:443/secure"),
                    "应该包含HTTPS链接");
        } else if (html.contains("http://example.com:80")) {
            assertTrue(result.getExternalLinks().contains("http://example.com:80/normal") ||
                       result.getInternalLinks().contains("http://example.com:80/normal"),
                    "应该包含HTTP链接");
        } else if (html.contains("ftp://")) {
            assertTrue(result.getExternalLinks().contains("ftp://example.com/file.zip")||
                    result.getInternalLinks().contains("ftp://example.com/file.zip"),
                    "应该包含FTP链接");
        }
    }
}
