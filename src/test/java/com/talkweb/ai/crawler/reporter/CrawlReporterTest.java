package com.talkweb.ai.crawler.reporter;

import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.reporter.CrawlReporter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CrawlReporter 类的单元测试。
 */
public class CrawlReporterTest {

    private CrawlReporter crawlReporter;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    public void setUp() {
        crawlReporter = new CrawlReporter(tempDir.toString());
    }

    @Test
    public void testLog() throws IOException {
        crawlReporter.log("INFO", "Test log message");
        
        Path logFilePath = tempDir.resolve("crawl_log.jsonl");
        assertTrue(Files.exists(logFilePath));
        String logContent = new String(Files.readAllBytes(logFilePath));
        assertTrue(logContent.contains("INFO"));
        assertTrue(logContent.contains("Test log message"));
    }

    @Test
    public void testRecordSuccess() throws IOException {
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/test");
        pageData.setFetchTimestamp(LocalDateTime.now());
        
        crawlReporter.recordSuccess(pageData);
        
        Path logFilePath = tempDir.resolve("crawl_log.jsonl");
        assertTrue(Files.exists(logFilePath));
        String logContent = new String(Files.readAllBytes(logFilePath));
        assertTrue(logContent.contains("INFO"));
        assertTrue(logContent.contains("成功抓取页面: http://example.com/test"));
    }

    @Test
    public void testRecordFailure() throws IOException {
        String url = "http://example.com/error";
        String errorMessage = "Connection failed";
        
        crawlReporter.recordFailure(url, errorMessage);
        
        Path logFilePath = tempDir.resolve("crawl_log.jsonl");
        Path errorLogFilePath = tempDir.resolve("errors.log");
        assertTrue(Files.exists(logFilePath));
        assertTrue(Files.exists(errorLogFilePath));
        
        String logContent = new String(Files.readAllBytes(logFilePath));
        assertTrue(logContent.contains("ERROR"));
        assertTrue(logContent.contains("抓取页面失败: http://example.com/error"));
        
        String errorLogContent = new String(Files.readAllBytes(errorLogFilePath));
        assertTrue(errorLogContent.contains(url));
        assertTrue(errorLogContent.contains(errorMessage));
    }

    @Test
    public void testGenerateReport() throws IOException {
        PageData pageData = new PageData();
        pageData.setUrl("http://example.com/test");
        pageData.setFetchTimestamp(LocalDateTime.now());
        
        crawlReporter.recordSuccess(pageData);
        crawlReporter.recordFailure("http://example.com/error", "Connection failed");
        crawlReporter.generateReport();
        
        Path reportPath = tempDir.resolve("crawl_report.json");
        assertTrue(Files.exists(reportPath));
        String reportContent = new String(Files.readAllBytes(reportPath));
        assertTrue(reportContent.contains("successfulPages"));
        assertTrue(reportContent.contains("failedPages"));
        assertTrue(reportContent.contains("errorLinks"));
    }
}