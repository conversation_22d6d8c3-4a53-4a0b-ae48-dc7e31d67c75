package com.talkweb.ai.crawler.coordinator;

import com.talkweb.ai.crawler.config.CrawlerConfig;
import com.talkweb.ai.crawler.downloader.FileDownloader;
import com.talkweb.ai.crawler.fetcher.IFetcher;
import com.talkweb.ai.crawler.metrics.MetricsExporter;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.parser.ContentParser;
import com.talkweb.ai.crawler.storage.StorageManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

/**
 * CrawlCoordinator 类的单元测试。
 */
@ExtendWith(MockitoExtension.class)
public class CrawlCoordinatorTest {

    @TempDir
    Path tempDir;
    @Mock
    private IFetcher fetcher;
    @Mock
    private ContentParser contentParser;
    @Mock
    private FileDownloader fileDownloader;
    @Mock
    private StorageManager storageManager;
    @Mock
    private MetricsExporter metricsExporter;

    private CrawlCoordinator crawlCoordinator;

    @BeforeEach
    public void setUp() {
        CrawlerConfig testConfig = new CrawlerConfig.Builder()
            .setStartUrl("http://example.com") // Default, can be overridden
            .setAllowedDomains(List.of("example.com"))
            .setMaxDepth(2)
            .setOutputDir("./test_output")
            .setMaxConnectionsPerDomain(5)
            .setConnectionTimeout(5000)
            .setReadTimeout(10000)
            .setMaxRetries(3)
            .setEnableDynamicFetcher(false) // Default for most tests
            .build();
        // storageManager= new StorageManager(testConfig.getOutputDir());
        crawlCoordinator = new CrawlCoordinator(testConfig, 2, 2, metricsExporter, fetcher, contentParser, storageManager, fileDownloader);
    }

    @Test
    public void testStartCrawl_AllowedDomain() throws IOException {
        String startUrl = "http://example.com";
        String htmlContent = "<html><body>Test</body></html>";
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(Collections.emptyList());
        pageData.setHtmlContent(htmlContent);
        when(fetcher.fetchPageContent(startUrl)).thenReturn(pageData);
        when(contentParser.parse(pageData)).thenReturn(pageData);
        when(storageManager.savePage(pageData)).thenReturn(Path.of("./test_output/example.com/page.html"));
        crawlCoordinator.startCrawl(startUrl);

        verify(fetcher, timeout(1000).times(1)).fetchPageContent(startUrl);
        verify(contentParser, timeout(1000).times(1)).parse(pageData);
        verify(storageManager, timeout(1000).times(1)).savePage(pageData);
    }

    @Test
    public void testStartCrawl_NotAllowedDomain() throws IOException {
        String startUrl = "http://notallowed.com";

        crawlCoordinator.startCrawl(startUrl);

        verify(fetcher, timeout(1000).times(0)).fetchPageContent(anyString());
        verify(contentParser, timeout(1000).times(0)).parse(anyString(), anyString());
        verify(storageManager, timeout(1000).times(0)).savePage(any());
    }

    @Test
    public void testCrawl_MaxDepthExceeded() throws IOException {
        String startUrl = "http://example.com";
        String htmlContent = "<html><body>Test</body></html>";

        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(Arrays.asList("http://example.com/page1.html", "http://example.com/page2.html"));
        pageData.setHtmlContent(htmlContent);

        when(fetcher.fetchPageContent(startUrl)).thenReturn(pageData);
        when(contentParser.parse(pageData)).thenReturn(pageData);
        when(storageManager.savePage(pageData)).thenReturn(Path.of("./test_output/example.com/page.html"));

        // 模拟子页面
        PageData page1Data = new PageData();
        page1Data.setUrl("http://example.com/page1.html");
        page1Data.setInternalLinks(List.of("http://example.com/page1/subpage1.html"));
        page1Data.setHtmlContent("<html><body>Page 1</body></html>");


        when(fetcher.fetchPageContent("http://example.com/page1.html")).thenReturn(page1Data);
        when(contentParser.parse(page1Data)).thenReturn(page1Data);
        when(storageManager.savePage(page1Data)).thenReturn(Path.of("./test_output/example.com/page1.html"));


//        // 模拟子页面
//        PageData subpage1 = new PageData();
//        subpage1.setUrl("http://example.com/page1/subpage1.html");
//        //subpage1.setInternalLinks(List.of("http://example.com/page1/subpage1.html"));
//        subpage1.setHtmlContent("<html><body>subpage1</body></html>");
//
//        when(fetcher.fetchPageContent("http://example.com/page1/subpage1.html")).thenReturn(subpage1);
//        when(contentParser.parse(subpage1)).thenReturn(subpage1);
//        when(storageManager.savePage(subpage1)).thenReturn(Path.of("./test_output/example.com/page1/subpage1.html"));


        PageData d = crawlCoordinator.startCrawl(startUrl);

        assertNotNull(d, "Crawl should return a PageData object");
        // 验证页面1被访问但子页面没有被访问（因为超过最大深度）
        verify(fetcher, timeout(1000).times(1)).fetchPageContent(startUrl);
        verify(fetcher, timeout(1000).times(1)).fetchPageContent("http://example.com/page1.html");
        verify(fetcher, timeout(1000).times(0)).fetchPageContent("http://example.com/page1/subpage1.html");
    }

    @Test
    public void testParallelCrawling() throws IOException, InterruptedException {
        // 设置多个URL和页面数据
        String startUrl = "http://example.com";
        List<String> internalLinks = Arrays.asList(
            "http://example.com/page1.html",
            "http://example.com/page2.html",
            "http://example.com/page3.html",
            "http://example.com/page4.html",
            "http://example.com/page5.html"
        );

        // 模拟主页
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(internalLinks);
        pageData.setHtmlContent("<html><body>Main Page</body></html>");

        when(fetcher.fetchPageContent(startUrl)).thenReturn(pageData);
        when(contentParser.parse(pageData)).thenReturn(pageData);
        when(storageManager.savePage(pageData)).thenReturn(Path.of("./test_output/example.com/index.html"));

        // 模拟子页面响应
        for (String link : internalLinks) {
            PageData subPageData = new PageData();
            subPageData.setUrl(link);
            subPageData.setInternalLinks(Collections.emptyList());
            subPageData.setHtmlContent("<html><body>" + link + " content</body></html>");

            when(fetcher.fetchPageContent(link)).thenReturn(subPageData);
            when(contentParser.parse(subPageData)).thenReturn(subPageData);
            when(storageManager.savePage(subPageData)).thenReturn(Path.of("./test_output/example.com/" + link.substring(link.lastIndexOf('/') + 1) + ".html"));
        }

        // 创建CountDownLatch来等待爬取完成
        CountDownLatch latch = new CountDownLatch(1);
        new Thread(() -> {
            crawlCoordinator.startCrawl(startUrl);
            latch.countDown();
        }).start();

        // 等待爬取完成或超时
        assertTrue(latch.await(5, TimeUnit.SECONDS));

        // 验证所有页面都被爬取
        verify(fetcher, timeout(2000).times(1)).fetchPageContent(startUrl);
        for (String link : internalLinks) {
            verify(fetcher, timeout(2000).times(1)).fetchPageContent(link);
        }
    }

    @Test
    public void testDomainSemaphoreLimit() throws IOException {
        // 创建限制并发连接为1的配置
        CrawlerConfig limitedConfig = new CrawlerConfig.Builder()
            .setStartUrl("http://example.com")
            .setAllowedDomains(List.of("example.com"))
            .setMaxDepth(2)
            .setOutputDir(tempDir.toString())
            .setMaxConnectionsPerDomain(1) // 限制每个域名只有1个并发连接
            .setConnectionTimeout(500)
            .setReadTimeout(1000)
            .setMaxRetries(3)
            .build();

        // 创建新的协调器
        CrawlCoordinator limitedCoordinator = new CrawlCoordinator(
            limitedConfig, 2, 2, metricsExporter, fetcher, contentParser, storageManager, fileDownloader);

        // 设置模拟数据
        String startUrl = "http://example.com";
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(Arrays.asList("http://example.com/page1.html", "http://example.com/page2.html"));
        pageData.setHtmlContent("<html><body>Test</body></html>");

//        when(fetcher.fetchPageContent("http://example.com")).thenReturn(pageData);

        PageData result = new PageData();
        result.setUrl("http://example.com/page1.html");
        result.setInternalLinks(Collections.emptyList());
        result.setHtmlContent("<html><body>xx</body></html>");

        // 设置fetchPageContent方法被调用时阻塞一段时间，模拟长时间请求
        when(fetcher.fetchPageContent(anyString())).thenAnswer(invocation -> {
           // Thread.sleep(500); // 模拟网络延迟
            String url = invocation.getArgument(0);
            System.out.println("Fetching URL: " + url);
            if( url.equals("http://example.com/page1.html")) {
                result.setUrl("http://example.com/page1.html");
                return result; // 返回第一个页面数据
            } else if (url.equals("http://example.com/page2.html")) {
                result.setUrl("http://example.com/page2.html");
                return result; // 返回第一个页面数据
            }else {
                return pageData; // 返回其他页面数据
            }
        });

        when(contentParser.parse(any(PageData.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(storageManager.savePage(any(PageData.class))).thenReturn(Path.of(tempDir.toString(), "page.html"));

        // 开始爬取
        limitedCoordinator.startCrawl(startUrl);

        // 验证fetchPageContent方法被调用的次数和顺序
        verify(fetcher, timeout(3000).times(3)).fetchPageContent(anyString());
    }

    @Test
    public void testCrawlWithExternalLinks() throws IOException {
        String startUrl = "http://example.com";

        // 创建包含外部链接的页面
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(List.of("http://example.com/internal.html"));
        pageData.setExternalLinks(List.of("http://external.com/page.html")); // 添加外部链接
        pageData.setHtmlContent("<html><body>Page with external link</body></html>");

        // 创建内部页面
        PageData internalPage = new PageData();
        internalPage.setUrl("http://example.com/internal.html");
        internalPage.setInternalLinks(Collections.emptyList());
        internalPage.setExternalLinks(Collections.emptyList());
        internalPage.setHtmlContent("<html><body>Internal page</body></html>");

        // 创建外部页面
        PageData externalPage = new PageData();
        externalPage.setUrl("http://external.com/page.html");
        externalPage.setInternalLinks(Collections.emptyList());
        externalPage.setExternalLinks(Collections.emptyList());
        externalPage.setHtmlContent("<html><body>External page</body></html>");

        // 设置模拟行为
        when(fetcher.fetchPageContent(startUrl)).thenReturn(pageData);
        when(fetcher.fetchPageContent("http://example.com/internal.html")).thenReturn(internalPage);
        when(fetcher.fetchPageContent("http://external.com/page.html")).thenReturn(externalPage);

        when(contentParser.parse(any(PageData.class))).thenAnswer(inv -> inv.getArgument(0));
        when(storageManager.savePage(any(PageData.class))).thenReturn(Path.of(tempDir.toString(), "page.html"));

        // 创建一个CrawlCoordinator，允许爬取外部链接（externalLinkDepth = 1）
        CrawlerConfig config = new CrawlerConfig.Builder()
            .setStartUrl("http://example.com")
            .setAllowedDomains(List.of("example.com","external.com")) // 只允许example.com域
            .setMaxDepth(2)
            .setOutputDir(tempDir.toString())
            .setMaxConnectionsPerDomain(5)
            .build();

        CrawlCoordinator externalLinkCoordinator = new CrawlCoordinator(
            config, 1, 2, metricsExporter, fetcher, contentParser, storageManager, fileDownloader);

        // 开始爬取
        externalLinkCoordinator.startCrawl(startUrl);

        // 验证内部和外部链接都被爬取
        verify(fetcher, timeout(2000).times(1)).fetchPageContent(startUrl);
        verify(fetcher, timeout(2000).times(1)).fetchPageContent("http://example.com/internal.html");
        verify(fetcher, timeout(2000).times(1)).fetchPageContent("http://external.com/page.html");
    }

    @Test
    public void testCrawlCycleDetection() throws IOException {
        String startUrl = "http://example.com";

        // 创建循环引用的页面
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(List.of("http://example.com/page1.html"));
        pageData.setHtmlContent("<html><body><a href=\"http://example.com/page1\"/>Main page</body></html>");

        PageData page1Data = new PageData();
        page1Data.setUrl("http://example.com/page1.html");
        page1Data.setInternalLinks(List.of(startUrl)); // 循环回主页
        page1Data.setHtmlContent("<html><body>Page 1 with cycle back to main</body></html>");

        // 设置模拟行为
        when(fetcher.fetchPageContent(startUrl)).thenReturn(pageData);
        when(fetcher.fetchPageContent("http://example.com/page1.html")).thenReturn(page1Data);

        when(contentParser.parse(any(PageData.class))).thenAnswer(inv -> inv.getArgument(0));
        when(storageManager.savePage(any(PageData.class))).thenReturn(Path.of(tempDir.toString(), "page.html"));

        // 开始爬取
        crawlCoordinator.startCrawl(startUrl);

        // 验证每个URL只被爬取一次（避免无限循环）
        verify(fetcher, timeout(2000).times(1)).fetchPageContent(startUrl);
        verify(fetcher, timeout(2000).times(1)).fetchPageContent("http://example.com/page1.html");
    }

    @Test
    public void testIsEffectivelyIdle() throws IOException, InterruptedException {
        String startUrl = "http://example.com";

        // 创建简单页面数据
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(Collections.emptyList());
        pageData.setHtmlContent("<html><body>Test</body></html>");

        when(fetcher.fetchPageContent(startUrl)).thenReturn(pageData);
        when(contentParser.parse(pageData)).thenReturn(pageData);
        when(storageManager.savePage(pageData)).thenReturn(Path.of("./test_output/example.com/page.html"));

        // 开始爬取
        crawlCoordinator.startCrawl(startUrl);

        // 等待一段时间让爬取完成
        Thread.sleep(500);

        // 验证爬取完成后协调器处于空闲状态
        assertTrue(crawlCoordinator.isEffectivelyIdle(), "Coordinator should be idle after crawling is complete");
    }

    @Test
    public void testGracefulShutdown() throws IOException, InterruptedException {
        String startUrl = "http://example.com";

        // 创建包含多个链接的页面
        PageData pageData = new PageData();
        pageData.setUrl(startUrl);
        pageData.setInternalLinks(Arrays.asList(
            "http://example.com/page1",
            "http://example.com/page2",
            "http://example.com/page3"
        ));
        pageData.setHtmlContent("<html><body>Main page with links</body></html>");

        // 模拟页面获取的行为
        when(fetcher.fetchPageContent(anyString())).thenAnswer(inv -> {
            Thread.sleep(300); // 模拟网络延迟
            String url = inv.getArgument(0);
            PageData result = new PageData();
            result.setUrl(url);
            result.setInternalLinks(Collections.emptyList());
            result.setHtmlContent("<html><body>" + url + "</body></html>");
            return result;
        });

        when(contentParser.parse(any(PageData.class))).thenAnswer(inv -> inv.getArgument(0));
        when(storageManager.savePage(any(PageData.class))).thenReturn(Path.of(tempDir.toString(), "page.html"));

        // 在单独的线程中启动爬取
        Thread crawlThread = new Thread(() -> {
            crawlCoordinator.startCrawl(startUrl);
        });
        crawlThread.start();

        // 等待一小段时间让爬取开始
        Thread.sleep(100);

        // 请求优雅关闭
        crawlCoordinator.shutdown();

        // 等待爬取线程完成
        crawlThread.join(2000);

        // 验证shutdown后所有任务完成
        assertTrue(crawlCoordinator.isEffectivelyIdle(), "Coordinator should be idle after shutdown");
    }
}
