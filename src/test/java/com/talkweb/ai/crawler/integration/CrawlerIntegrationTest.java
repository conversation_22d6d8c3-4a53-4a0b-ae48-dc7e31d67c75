package com.talkweb.ai.crawler.integration;

import com.talkweb.ai.crawler.coordinator.CrawlCoordinator;
import com.talkweb.ai.crawler.config.CrawlerConfig;
import com.talkweb.ai.crawler.fetcher.IFetcher;
import com.talkweb.ai.crawler.fetcher.PageFetcher;
import com.talkweb.ai.crawler.metrics.MetricsExporter;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.parser.ContentParser;
import com.talkweb.ai.crawler.storage.StorageManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mockito;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

/**
 * 爬虫系统的集成测试，使用 Testcontainers 验证模块间交互。
 */
@Testcontainers
public class CrawlerIntegrationTest {

    @TempDir
    Path tempDir;

    private CrawlCoordinator crawlCoordinator;
    private IFetcher pageFetcher;
    private ContentParser contentParser;
    private StorageManager storageManager;

    @Container
    private final GenericContainer<?> webServer = new GenericContainer<>(DockerImageName.parse("nginx:alpine"))
        .withExposedPorts(80)
        .withClasspathResourceMapping("test-web-content", "/usr/share/nginx/html", org.testcontainers.containers.BindMode.READ_ONLY);

    @BeforeEach
    public void setUp() throws IOException {
        pageFetcher = Mockito.mock(IFetcher.class);
        contentParser = new ContentParser(tempDir.toString());
        storageManager = new StorageManager(tempDir.toString());
        MetricsExporter mockMetrics = Mockito.mock(MetricsExporter.class);

        CrawlerConfig config = new CrawlerConfig.Builder()
            .setStartUrl("http://localhost") // Actual start URL is dynamic from test container
            .setAllowedDomains(List.of("localhost"))
            .setMaxDepth(2)
            .setOutputDir(tempDir.toString())
            .setMaxConnectionsPerDomain(1)
            .setConnectionTimeout(5000)
            .setReadTimeout(10000)
            .setMaxRetries(1)
            .setEnableDynamicFetcher(false)
            .build();
        crawlCoordinator = new CrawlCoordinator(config, 2, 2, mockMetrics, pageFetcher, contentParser, storageManager);
    }

    @Test
    public void testCrawlerIntegration() throws Exception {
        String testUrl = "http://localhost:" + webServer.getFirstMappedPort() + "/index.html";
        String htmlContent = "<html><body><h1>Test Page1</h1><p>This is a test page.</p></body></html>";
        PageData pageData = new PageData();
        pageData.setUrl(testUrl);
        pageData.setTitle("Test Page");
        pageData.setHtmlContent("# Test Page\n\n#Test Page1\n\nThis is a test page.");
        pageData.setFetchTimestamp(java.time.LocalDateTime.now());
        pageData.setMetaData(new java.util.HashMap<>());
        pageData.setInternalLinks(Collections.emptyList());
        pageData.setExternalLinks(Collections.emptyList());
        pageData.setAttachments(Collections.emptyList());
        pageData.setHtmlContent(htmlContent);

        when(pageFetcher.fetchPageContent(testUrl)).thenReturn(pageData);
      //  when(contentParser.parse(htmlContent, testUrl)).thenReturn(pageData);

        crawlCoordinator.startCrawl(testUrl);

       // Thread.sleep(20000); // 等待爬虫处理完成

        crawlCoordinator.shutdown();

        Path savedFilePath = tempDir.resolve("localhost:" + webServer.getFirstMappedPort() + "/index.html");
        assertTrue(Files.exists(savedFilePath), "Markdown file should be saved at expected path");
    //    String savedContent = new String(Files.readAllBytes(savedFilePath));
//        assertTrue(savedContent.contains("# Test Page"), "Saved content should contain the markdown title");
    //    assertTrue(savedContent.contains("# Test Page1"), "Saved content should contain the markdown heading");
    //    assertTrue(savedContent.contains("This is a test page."), "Saved content should contain the markdown body");
    }
}
