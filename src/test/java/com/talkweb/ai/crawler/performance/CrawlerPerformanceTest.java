package com.talkweb.ai.crawler.performance;

import com.talkweb.ai.crawler.fetcher.IFetcher;
import com.talkweb.ai.crawler.fetcher.PageFetcher;
import com.talkweb.ai.crawler.model.PageData;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.TimeUnit;

/**
 * 爬虫系统的性能测试，使用 JMH 进行基准测试。
 */
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Thread)
public class CrawlerPerformanceTest {

    private IFetcher pageFetcher;
    private String testUrl;

    @Setup
    public void setup() {
        pageFetcher = new PageFetcher(10000, 5000,1);
        testUrl = "https://example.com";
    }

    @Benchmark
    public void testPageFetch(Blackhole blackhole) throws Exception {
        PageData pageData = pageFetcher.fetchPageContent(testUrl);
        blackhole.consume(pageData.getHtmlContent());
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(CrawlerPerformanceTest.class.getSimpleName())
                .forks(1)
                .build();

        new Runner(opt).run();
    }
}
