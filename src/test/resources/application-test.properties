# Test configuration to disable CLI runner during tests
crawler.cli.enabled=false

# Test database configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Disable security for tests
spring.security.user.name=test
spring.security.user.password=test

# Logging configuration for tests
logging.level.com.talkweb.ai=DEBUG
logging.level.org.springframework.web=DEBUG
