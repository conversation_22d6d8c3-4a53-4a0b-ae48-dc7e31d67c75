# 当前任务

## 目标
**实现内容解析模块，从HTML中提取关键信息。**

## 背景
我们已经成功集成了动态页面抓取能力（通过配置 `DynamicPageFetcher`）。现在，爬虫可以获取到完整的HTML内容。下一步是将这些HTML内容解析，提取出有用的信息，例如文本内容、标题、链接等，为后续的Markdown转换和数据存储做准备。

## 下一步行动
1.  **选择或实现HTML解析库**:
    *   调研并选择一个合适的Java HTML解析库（例如 Jsoup）。
    *   如果已有 `ContentParser.java`，则检查其现有功能是否满足需求，或是否需要基于所选库进行重构/增强。
2.  **定义提取规则**: 确定需要从HTML中提取哪些具体信息。例如：
    *   页面主文本内容（去除导航、广告、脚本等）。
    *   页面标题。
    *   页面内的所有链接（用于进一步抓取）。
    *   可能的元数据（作者、发布日期等）。
3.  **实现解析逻辑**: 在 `ContentParser` 类中实现具体的解析方法，该方法接收 `PageData` 对象（包含HTML内容），输出结构化的解析结果。
4.  **单元测试**: 为 `ContentParser` 编写单元测试，覆盖不同的HTML结构和提取场景。

## 目标
**验证动态抓取功能的配置是否成功。**

## 已完成的行动
1.  **确认代码能力**: 发现 `CrawlCoordinator` 已具备根据配置选择 `DynamicPageFetcher` 的能力。
2.  **分析配置加载**: 确认 `ConfigManager` 通过 `crawler.properties` 文件或命令行参数来管理配置。
3.  **完成配置**: 创建了 `crawler.properties` 文件，并明确设置 `enableDynamicFetcher=true`。

## 下一步行动
1.  **运行爬虫**: 执行 `CrawlerMain` 类来启动爬虫。
2.  **验证日志**: 观察控制台输出，确认看到 "DynamicPageFetcher is enabled" 和爬虫成功抓取页面的日志。
3.  **检查输出**: 查看在 `crawler.properties` 中配置的输出目录（`./output`），确认有Markdown文件生成。

## 目标
**集成 `DynamicPageFetcher` 到爬虫核心逻辑中。**

## 背景
根据最新的代码审查，我们发现项目已经存在一个基于Playwright的动态页面抓取器 `DynamicPageFetcher.java`。这完全满足了我们处理JavaScript渲染页面的新需求。因此，任务的重点从“实现”转变为“集成”。

## 下一步行动
1.  **定位调用点**: 找到当前爬虫逻辑中调用 `PageFetcher` 的地方。
2.  **替换实现**: 将调用 `PageFetcher` 的代码修改为调用 `DynamicPageFetcher`。
3.  **依赖注入 (可选)**: 考虑使用工厂模式或依赖注入的方式，使 `Fetcher` 的选择更加灵活，便于未来在静态和动态抓取之间切换。
4.  **测试**: 运行或编写新的测试用例，确保 `DynamicPageFetcher` 在集成后能正常工作。

## 目标
- 集成动态页面渲染能力到爬虫项目中，以支持JavaScript密集型网站内容的正确获取。

## 背景
- 用户明确新需求：爬虫必须能够执行页面中的JavaScript，获取动态加载和渲染后的完整HTML内容，然后才能进行后续的解析、转换和附件下载。
- 此变更将显著影响页面的获取方式，并可能引入新的核心组件和技术依赖。
- [projectRoadmap.md](docs/projectRoadmap.md:0:0-0:0) 已更新，加入了与动态渲染相关的新阶段和任务。

## 下一步行动
- 根据测试结果，修复任何新发现的问题或确认重构成功。
- 更新 `projectRoadmap.md` 中相关任务的进度。
