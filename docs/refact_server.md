# 爬虫服务化改造设计文档

## 1. 概述

本文档详细描述了将现有爬虫改造为基于Spring Boot的RESTful服务的详细设计方案。改造后的爬虫将支持通过HTTP API进行任务管理，并提供Web界面用于监控和管理爬取任务。

## 2. 功能需求

### 2.1 命令行参数增强
- 增加 `-server` 参数，用于以Web服务方式运行爬虫
- 保留现有命令行参数，用于向后兼容

### 2.2 REST API 接口

#### 2.2.1 任务管理接口

1. **创建爬取任务**
   - `POST /api/tasks`
   - 请求体：
     ```json
     {
       "url": "https://example.com",
       "maxDepth": 3,
       "outputDir": "/path/to/output",
       "allowedDomains": ["example.com"],
       "maxConnectionsPerDomain": 5,
       "enableDynamicFetcher": true,
       "callbackUrl": "https://callback.example.com"
     }
     ```
   - 响应：
     ```json
     {
       "taskId": "uuid-1234-5678",
       "status": "PENDING",
       "startTime": "2025-06-23T16:00:00Z",
       "message": "Task created successfully"
     }
     ```

2. **查询任务状态**
   - `GET /api/tasks/{taskId}`
   - 响应：
     ```json
     {
       "taskId": "uuid-1234-5678",
       "status": "RUNNING",
       "progress": 45,
       "startTime": "2025-06-23T16:00:00Z",
       "endTime": null,
       "urlsCrawled": 124,
       "urlsFailed": 3,
       "currentUrl": "https://example.com/page42"
     }
     ```

3. **取消任务**
   - `POST /api/tasks/{taskId}/cancel`
   - 响应：
     ```json
     {
       "taskId": "uuid-1234-5678",
       "status": "CANCELLED",
       "message": "Task cancelled successfully"
     }
     ```

4. **查询任务列表**
   - `GET /api/tasks`
   - 查询参数：`?status=RUNNING&page=0&size=20`
   - 响应：
     ```json
     {
       "content": [
         {
           "taskId": "uuid-1234-5678",
           "url": "https://example.com",
           "status": "RUNNING",
           "startTime": "2025-06-23T16:00:00Z",
           "endTime": null,
           "progress": 45
         }
       ],
       "page": 0,
       "size": 20,
       "totalElements": 1,
       "totalPages": 1
     }
     ```

### 2.3 任务管理UI

1. **任务列表页面**
   - 显示所有爬取任务的摘要信息
   - 支持按状态筛选
   - 支持分页
   - 每个任务显示：任务ID、URL、状态、开始时间、进度条

2. **任务详情页面**
   - 显示任务详细状态
   - 实时更新爬取进度
   - 显示已爬取URL列表
   - 显示错误日志
   - 提供取消按钮

3. **新建任务表单**
   - URL输入框
   - 爬取深度设置
   - 输出目录设置
   - 高级选项（连接超时、重试次数等）

### 2.4 API文档

- 集成Swagger UI
- 访问路径：`/swagger-ui.html`
- 提供完整的API文档和交互式测试界面

## 3. 非功能需求

### 3.1 性能需求
- 支持至少100个并发爬取任务
- API响应时间<100ms（非爬取操作）
- 支持水平扩展

### 3.2 安全需求
- 支持API密钥认证
- 请求频率限制
- 输入参数验证和过滤

### 3.3 可靠性需求
- 任务状态持久化，支持服务重启后恢复
- 完善的错误处理和日志记录
- 资源使用监控和告警

## 4. 技术架构

### 4.1 组件图

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|   Web Layer     |<--->|  Service Layer  |<--->|  Crawler Core   |
|  (Controllers)  |     |  (Task Mgmt)    |     |  (CrawlCoord.)  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
         ^                      ^
         |                      |
         v                      v
+------------------+     +------------------+
|                  |     |                  |
|  Swagger UI     |     |   Database       |
|  (API Docs)     |     |  (Task State)    |
|                  |     |                  |
+------------------+     +------------------+
```

### 4.2 技术选型

- **Web框架**：Spring Boot 2.7.x
- **API文档**：SpringDoc OpenAPI 3.0
- **前端**：Thymeleaf + Bootstrap 5 + jQuery
- **数据库**：H2（嵌入式，开发环境）
- **任务队列**：Spring @Async + CompletableFuture
- **缓存**：Caffeine
- **构建工具**：Maven
- **监控**：Micrometer + Prometheus + Grafana

## 5. 实施计划

### 阶段1：基础框架搭建（2天）
1. 添加Spring Boot依赖
2. 实现基本项目结构
3. 配置应用属性
4. 实现健康检查端点

### 阶段2：任务管理API（3天）
1. 设计并实现任务模型
2. 实现任务创建、查询、取消API
3. 实现任务状态管理
4. 添加输入验证和异常处理

### 阶段3：集成爬虫核心（2天）
1. 将CrawlCoordinator集成到服务层
2. 实现异步任务执行
3. 添加任务状态更新机制
4. 实现任务取消功能

### 阶段4：Web界面（3天）
1. 实现任务列表页面
2. 实现任务详情页面
3. 实现新建任务表单
4. 添加实时状态更新

### 阶段5：API文档和测试（2天）
1. 集成Swagger UI
2. 添加API文档注解
3. 编写单元测试
4. 编写集成测试

### 阶段6：部署和监控（2天）
1. 配置生产环境
2. 设置监控和告警
3. 性能测试和调优
4. 编写部署文档

## 6. 详细任务清单

### 6.1 后端任务

1. **项目配置**
   - [√] 添加Spring Boot依赖
   - [√] 配置应用属性
   - [√] 配置日志
   - [√] 配置数据库

2. **领域模型**
   - [√] 设计任务模型
   - [√] 设计API请求/响应对象
   - [√] 设计异常类

3. **持久层**
   - [√] 实现任务仓库接口
   - [√] 配置JPA实体
   - [√] 添加数据迁移脚本

4. **服务层**
   - [√] 实现任务服务
   - [√] 集成CrawlCoordinator
   - [√] 实现异步任务执行
   - [√] 添加任务状态管理

5. **Web层**
   - [√] 实现REST控制器
   - [√] 添加输入验证
   - [√] 实现异常处理
   - [√] 添加API文档

6. **安全性**
   - [√] 添加API密钥认证
   - [√] 实现请求限流
   - [√] 添加CORS配置

### 6.2 前端任务

1. **页面模板**
   - [√] 创建基础布局
   - [√] 实现导航栏
   - [√] 添加页脚

2. **任务列表页面**
   - [√] 实现任务表格
   - [√] 添加分页控件
   - [√] 实现状态筛选
   - [√] 添加新建任务按钮

3. **任务详情页面**
   - [√] 显示任务详情
   - [√] 实现进度条
   - [√] 显示URL列表
   - [√] 添加取消按钮

4. **新建任务表单**
   - [√] 实现表单布局
   - [√] 添加表单验证
   - [√] 实现提交逻辑
   - [√] 添加加载状态

5. **实时更新**
   - [√] 实现WebSocket连接
   - [√] 添加事件监听
   - [√] 更新UI状态

## 7. 数据库设计

### 7.1 任务表 (crawl_task)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | VARCHAR(36) | 主键，UUID |
| url | VARCHAR(2048) | 爬取URL |
| status | VARCHAR(20) | 任务状态 |
| progress | INT | 进度(0-100) |
| start_time | TIMESTAMP | 开始时间 |
| end_time | TIMESTAMP | 结束时间 |
| max_depth | INT | 最大深度 |
| output_dir | VARCHAR(512) | 输出目录 |
| allowed_domains | TEXT | 允许的域名(JSON数组) |
| max_connections | INT | 最大连接数 |
| enable_dynamic | BOOLEAN | 是否启用动态渲染 |
| callback_url | VARCHAR(512) | 回调URL |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 7.2 爬取记录表 (crawl_record)

**索引建议**:
- `task_id` 和 `status` 的复合索引，用于快速查询特定任务的成功/失败记录。
- `created_at` 索引，用于按时间排序记录。

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| task_id | VARCHAR(36) | 外键，关联crawl_task |
| url | VARCHAR(2048) | 爬取的URL |
| status | VARCHAR(20) | 状态(SUCCESS/FAILED) |
| status_code | INT | HTTP状态码 |
| error_message | TEXT | 错误信息 |
| created_at | TIMESTAMP | 创建时间 |

## 8. API 详细设计

### 8.1 创建任务

**Endpoint**: `POST /api/tasks`

**请求头**:
```
Content-Type: application/json
X-API-Key: your-api-key
```

**请求体**:
```json
{
  "url": "https://example.com",
  "maxDepth": 3,
  "outputDir": "/data/crawler/output",
  "allowedDomains": ["example.com"],
  "maxConnectionsPerDomain": 5,
  "connectionTimeout": 30000,
  "readTimeout": 60000,
  "maxRetries": 3,
  "enableDynamicFetcher": true,
  "userAgent": "Mozilla/5.0 (compatible; MyCrawler/1.0)",
  "callbackUrl": "https://api.example.com/callbacks/123"
}
```

**响应 (201 Created)**:
```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "PENDING",
  "startTime": "2025-06-23T16:00:00Z",
  "message": "Task created successfully"
}
```

### 8.2 查询任务状态

**Endpoint**: `GET /api/tasks/{taskId}`

**响应 (200 OK)**:
```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "url": "https://example.com",
  "status": "RUNNING",
  "progress": 42,
  "startTime": "2025-06-23T16:00:00Z",
  "endTime": null,
  "urlsCrawled": 126,
  "urlsFailed": 3,
  "currentUrl": "https://example.com/page42",
  "maxDepth": 3,
  "outputDir": "/data/crawler/output",
  "createdAt": "2025-06-23T16:00:00Z",
  "updatedAt": "2025-06-23T16:05:30Z"
}
```

### 8.3 重试失败的任务

**Endpoint**: `POST /api/tasks/{taskId}/retry`

**说明**: 仅当任务状态为 `FAILED` 时可调用。该操作会重置任务状态为 `PENDING` 并重新加入待执行队列。

**响应 (200 OK)**:
```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "PENDING",
  "message": "Task has been requeued for retry."
}
```

### 8.4 删除任务

**Endpoint**: `DELETE /api/tasks/{taskId}`

**说明**: 仅当任务状态为 `COMPLETED`, `FAILED`, 或 `CANCELLED` 时可调用。此操作将从数据库中删除任务及其所有关联记录。物理文件是否删除可由查询参数控制。

**查询参数**:
- `deleteFiles` (boolean, optional, default: `false`): 如果为 `true`，将一并删除该任务爬取的所有物理文件。

**响应 (204 No Content)**

**Endpoint**: `GET /api/tasks/{taskId}`

**响应 (200 OK)**:
```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "url": "https://example.com",
  "status": "RUNNING",
  "progress": 42,
  "startTime": "2025-06-23T16:00:00Z",
  "endTime": null,
  "urlsCrawled": 126,
  "urlsFailed": 3,
  "currentUrl": "https://example.com/page42",
  "maxDepth": 3,
  "outputDir": "/data/crawler/output",
  "createdAt": "2025-06-23T16:00:00Z",
  "updatedAt": "2025-06-23T16:05:30Z"
}
```

## 8.5 任务状态生命周期管理

为了清晰地管理爬取任务，我们定义了以下状态及其流转路径：

- **PENDING**: 任务已创建，等待调度执行。
- **RUNNING**: 任务正在被爬虫核心处理。
- **COMPLETED**: 任务成功完成所有爬取。
- **FAILED**: 任务因不可恢复的错误而终止。
- **CANCELLED**: 任务被用户主动取消。

**状态流转图:**

```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建任务
    PENDING --> RUNNING: 调度器启动
    RUNNING --> COMPLETED: 爬取成功
    RUNNING --> FAILED: 发生严重错误
    RUNNING --> CANCELLED: 用户取消
    PENDING --> CANCELLED: 用户取消
    FAILED --> PENDING: 用户重试
    COMPLETED --> [*]: 用户删除
    FAILED --> [*]: 用户删除
    CANCELLED --> [*]: 用户删除
```

## 8.6 任务回调机制 (Webhook)

当任务完成（状态为 `COMPLETED` 或 `FAILED`）时，系统将向任务创建时指定的 `callbackUrl` 发送一个 `POST` 请求，通知外部系统。请求体格式如下：

```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "url": "https://example.com",
  "status": "COMPLETED",
  "startTime": "2025-06-23T16:00:00Z",
  "endTime": "2025-06-23T17:30:00Z",
  "summary": {
    "urlsCrawled": 500,
    "urlsFailed": 10,
    "totalFilesDownloaded": 450,
    "totalDataSize": "50 MB"
  },
  "errorInfo": null
}
```

## 9. UI/UX 设计

为了提供直观的任务管理体验，前端将包含以下页面：

### 9.1 任务列表页面 (`/tasks`)

- **功能**: 集中展示所有爬取任务。
- **组件**:
  - **表格**: 显示任务核心信息（ID, 目标URL, 状态, 进度, 创建时间）。
  - **筛选/排序**: 按状态、时间进行筛选和排序。
  - **操作按钮**: 每行提供“详情”、“取消”、“重试”、“删除”等快捷操作。
  - **新建按钮**: 页面右上角提供“新建任务”按钮，跳转到新建页面。

### 9.2 任务详情页面 (`/tasks/{taskId}`)

- **功能**: 提供单个任务的全面信息和实时状态。
- **组件**:
  - **摘要卡片**: 显示任务的所有配置参数。
  - **实时进度条**: 可视化展示任务进度。
  - **统计数据**: 已爬取URL数、失败URL数、下载数据量等。
  - **URL列表**: 分页展示已爬取和失败的URL列表。
  - **日志视图**: 实时展示爬虫核心输出的日志信息。

### 9.3 新建任务页面 (`/tasks/new`)

- **功能**: 通过表单创建一个新的爬取任务。
- **组件**:
  - **输入表单**: 包含所有 `CrawlConfig` 中的可配置项。
  - **默认值**: 为非必填项提供合理的默认值（如 `maxDepth=3`）。
  - **输入验证**: 前后端对URL格式、数字范围等进行校验。

## 10. 安全性设计

### 10.1 API 密钥认证

所有对 `/api/**` 的请求都必须通过 `X-API-Key` 请求头进行认证。

- **生成与分发**: API密钥应由管理员通过安全的后台接口或命令行工具生成。密钥应具有足够的随机性和长度。
- **存储**: 数据库中存储API密钥的哈希值（例如使用 BCrypt），绝不存储明文。每个密钥关联一个所有者或应用标识。
- **验证**: 创建一个Spring `Filter` 或 `Interceptor`，在请求处理前执行以下操作：
  1. 从 `X-API-Key` 请求头中提取密钥。
  2. 在数据库中查找对应的哈希值。
  3. 使用相同的哈希算法验证密钥是否匹配。
  4. 如果验证失败，返回 `401 Unauthorized`。

### 10.2 请求限流

为防止API被滥用，需要实施请求限流策略。

- **策略**: 可以基于API密钥或客户端IP地址进行限流。
- **实现**: 推荐使用 `Bucket4j` 或 `Resilience4j` 库，配置令牌桶算法，例如限制每个密钥每分钟最多请求100次。
- **响应**: 当请求超过限制时，返回 `429 Too Many Requests` 状态码。

## 11. 异常处理

### 9.1 错误响应格式

所有错误响应都遵循以下格式：

```json
{
  "timestamp": "2025-06-23T16:10:30Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Invalid request parameters",
  "path": "/api/tasks",
  "requestId": "abc123"
}
```

### 9.2 错误码定义

| HTTP 状态码 | 错误码 | 描述 |
|------------|--------|------|
| 400 | BAD_REQUEST | 请求参数错误 |
| 401 | UNAUTHORIZED | 未授权访问 |
| 403 | FORBIDDEN | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 409 | CONFLICT | 资源冲突 |
| 429 | TOO_MANY_REQUESTS | 请求过于频繁 |
| 500 | INTERNAL_SERVER_ERROR | 服务器内部错误 |
| 503 | SERVICE_UNAVAILABLE | 服务不可用 |

## 10. 部署架构

### 10.1 开发环境
- 使用嵌入式H2数据库
- 单节点部署
- 本地文件系统存储爬取结果

### 10.2 生产环境
- 使用MySQL/PostgreSQL作为主数据库
- 使用Redis作为缓存和分布式锁
- 使用MinIO/S3存储爬取结果
- 支持多实例部署和水平扩展
- 使用Nginx作为反向代理和负载均衡
- 使用Prometheus + Grafana进行监控

## 11. 监控与告警

### 11.1 监控指标
- 系统资源使用率（CPU、内存、磁盘、网络）
- JVM指标（堆内存、GC、线程）
- 应用指标（请求量、响应时间、错误率）
- 爬虫指标（任务数、URL爬取速率、成功率）

### 11.2 告警规则
- 系统资源使用率超过阈值
- 应用错误率上升
- 爬虫任务堆积
- 爬取成功率下降

## 12. 后续优化方向

1. **性能优化**
   - 实现分布式爬取
   - 优化URL去重算法
   - 实现增量爬取

2. **功能增强**
   - 支持更多认证方式
   - 实现自定义爬取规则
   - 添加数据清洗和转换功能

3. **可用性提升**
   - 实现任务优先级
   - 添加任务依赖关系
   - 支持断点续爬
