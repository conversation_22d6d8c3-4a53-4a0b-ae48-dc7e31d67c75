# AI 网页爬虫系统 - 文档索引

## 📚 文档概览

本项目提供了完整的文档体系，涵盖用户使用、开发指南、部署运维等各个方面。

## 📖 用户文档

### 1. [README.md](../README.md)
**项目概述和快速开始指南**
- 项目介绍和核心特性
- 系统要求和安装步骤
- 快速开始和使用示例
- 基础配置说明

### 2. [API文档.md](./API文档.md)
**REST API 详细说明**
- API 接口规范
- 请求/响应格式
- 错误码说明
- 使用示例和最佳实践
- 在线文档: http://localhost:8080/swagger-ui.html

### 3. [部署运维文档.md](./部署运维文档.md)
**部署和运维指南**
- 环境要求和依赖
- 部署方式（源码、Docker、集群）
- 配置管理和优化
- 监控告警和故障排查
- 备份恢复和安全配置

## 🔧 开发文档

### 4. [架构设计文档.md](./架构设计文档.md)
**系统架构设计**
- 整体架构和分层设计
- 核心组件详细说明
- 数据模型和流程设计
- 并发性能和错误处理
- 扩展性和安全设计

### 5. [代码库概要.md](./codebaseSummary.md)
**代码结构和组件说明**
- 项目结构和技术栈
- 核心组件架构
- 设计模式和原则
- 测试架构和监控体系
- 项目成熟度评估

## 📋 历史文档

### 6. [进度报告.md](./progress.md)
**项目开发进度**
- 各阶段完成情况
- 功能实现状态
- 测试覆盖报告

### 7. [服务化改造设计.md](./refact_server.md)
**Web 服务化改造方案**
- 改造需求和目标
- 技术架构选型
- 实施计划和任务清单
- API 设计和界面规划

### 8. [爬虫设计文档.md](./web_to_md_crawler_design2.md)
**爬虫核心设计**
- 爬虫功能需求
- 技术选型和架构
- 组件设计和交互
- 数据流和存储方案

### 9. [技术栈文档.md](./techStack.md)
**技术选型说明**
- 核心技术栈
- 依赖库选择
- 版本兼容性

## 🧪 测试文档

### 10. [测试修复总结.md](./test_fix_summary.md)
**测试问题修复记录**
- 测试失败原因分析
- 修复方案和实施
- 测试结果验证

### 11. [测试实现总结.md](./test_implementation_summary.md)
**测试实现详情**
- 测试架构设计
- 测试用例分类
- 测试工具和框架

### 12. [测试改进计划.md](./test_improvement_plan.md)
**测试体系改进**
- 测试覆盖分析
- 改进建议和计划
- 质量保证措施

## 📊 项目管理

### 13. [项目路线图.md](./projectRoadmap.md)
**项目发展规划**
- 功能路线图
- 版本计划
- 里程碑目标

### 14. [当前任务.md](./currentTask.md)
**当前开发任务**
- 正在进行的工作
- 待完成任务
- 优先级排序

## 🔗 在线资源

### 实时文档和监控
- **API 文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health
- **应用信息**: http://localhost:8080/actuator/info
- **监控指标**: http://localhost:8080/actuator/prometheus
- **Web 管理界面**: http://localhost:8080/tasks

### 开发工具
- **H2 数据库控制台**: http://localhost:8080/h2-console
- **日志文件**: `logs/crawler.log`
- **配置文件**: `application.properties`

## 📝 文档维护

### 文档更新原则
1. **及时更新**: 代码变更后及时更新相关文档
2. **版本控制**: 重要文档变更记录版本号
3. **格式统一**: 遵循 Markdown 格式规范
4. **内容准确**: 确保文档与实际实现一致

### 文档贡献指南
1. **新增文档**: 在 `docs/` 目录下创建
2. **更新索引**: 在本文档中添加链接
3. **格式检查**: 确保 Markdown 格式正确
4. **内容审核**: 提交前进行内容审核

### 文档反馈
如发现文档问题或有改进建议，请：
1. 提交 Issue 说明问题
2. 直接提交 Pull Request 修复
3. 联系文档维护者

## 📈 文档统计

### 文档数量
- **用户文档**: 3 个
- **开发文档**: 2 个
- **历史文档**: 7 个
- **测试文档**: 3 个
- **项目管理**: 2 个
- **总计**: 17 个文档

### 文档状态
- ✅ **最新**: 主要文档已更新到最新版本
- ✅ **完整**: 覆盖项目各个方面
- ✅ **准确**: 与代码实现保持一致
- ✅ **易用**: 结构清晰，便于查找

## 🎯 推荐阅读路径

### 新用户
1. [README.md](../README.md) - 了解项目概况
2. [API文档.md](./API文档.md) - 学习 API 使用
3. [部署运维文档.md](./部署运维文档.md) - 部署和配置

### 开发者
1. [架构设计文档.md](./架构设计文档.md) - 理解系统架构
2. [代码库概要.md](./codebaseSummary.md) - 熟悉代码结构
3. [测试文档](#-测试文档) - 了解测试体系

### 运维人员
1. [部署运维文档.md](./部署运维文档.md) - 部署和监控
2. [API文档.md](./API文档.md) - API 接口说明
3. [在线资源](#-在线资源) - 监控和管理工具

### 项目管理
1. [进度报告.md](./progress.md) - 项目进展
2. [项目路线图.md](./projectRoadmap.md) - 发展规划
3. [当前任务.md](./currentTask.md) - 当前状态

---

**文档索引版本**: 1.0  
**最后更新**: 2025-06-23  
**维护者**: 文档团队  
**反馈邮箱**: <EMAIL>
