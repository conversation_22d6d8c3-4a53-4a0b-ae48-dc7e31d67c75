# AI 网页爬虫系统 - 架构设计文档

## 1. 系统概述

AI 网页爬虫系统是一个基于 Spring Boot 的现代化 Web 爬虫解决方案，支持静态和动态页面抓取，提供完整的任务管理和监控功能。

### 1.1 设计目标
- **高性能**: 支持多线程并发抓取，每域名连接数可控
- **高可用**: 完善的错误处理和恢复机制
- **易扩展**: 模块化设计，支持插件式扩展
- **易使用**: 提供 Web 界面和 REST API 两种使用方式

### 1.2 核心功能
- 网页内容抓取和解析
- HTML 到 Markdown 转换
- 任务管理和状态跟踪
- 文件下载和存储管理
- 实时监控和指标收集

## 2. 技术架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "用户接口层"
        A[Web UI] 
        B[REST API]
        C[Swagger UI]
    end
    
    subgraph "业务服务层"
        D[TaskController]
        E[TaskService]
        F[ConfigManager]
    end
    
    subgraph "爬虫核心层"
        G[CrawlCoordinator]
        H[PageFetcher]
        I[DynamicPageFetcher]
        J[ContentParser]
        K[StorageManager]
        L[FileDownloader]
    end
    
    subgraph "数据访问层"
        M[TaskRepository]
        N[H2 Database]
    end
    
    subgraph "基础设施层"
        O[Prometheus监控]
        P[日志系统]
        Q[配置管理]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> G
    E --> M
    G --> H
    G --> I
    G --> J
    G --> K
    G --> L
    M --> N
    G --> O
    G --> P
    E --> Q
```

### 2.2 分层架构

#### 用户接口层 (Presentation Layer)
- **Web UI**: 基于 Thymeleaf 的任务管理界面
- **REST API**: RESTful 风格的 HTTP API
- **Swagger UI**: 交互式 API 文档

#### 业务服务层 (Service Layer)
- **TaskController**: 处理 HTTP 请求和响应
- **TaskService**: 核心业务逻辑处理
- **ConfigManager**: 配置管理和参数验证

#### 爬虫核心层 (Core Layer)
- **CrawlCoordinator**: 爬取任务协调和调度
- **PageFetcher**: 静态页面抓取
- **DynamicPageFetcher**: 动态页面抓取
- **ContentParser**: 内容解析和链接提取
- **StorageManager**: 文件存储管理
- **FileDownloader**: 附件下载处理

#### 数据访问层 (Data Access Layer)
- **TaskRepository**: 任务数据访问接口
- **H2 Database**: 嵌入式关系数据库

#### 基础设施层 (Infrastructure Layer)
- **监控系统**: Prometheus 指标收集
- **日志系统**: Logback 日志管理
- **配置管理**: Spring Boot 配置体系

## 3. 核心组件设计

### 3.1 CrawlCoordinator (爬取协调器)

```java
@Component
public class CrawlCoordinator {
    // 核心职责
    - 任务调度和URL队列管理
    - 并发控制和线程池管理
    - 域名访问频率控制
    - 递归深度控制
    - 错误处理和重试机制
}
```

**设计特点**:
- 使用线程池实现并发抓取
- 基于信号量控制每域名并发数
- 使用 ConcurrentHashMap 实现 URL 去重
- 支持优雅关闭和任务取消

### 3.2 页面抓取器 (Fetcher)

#### PageFetcher (静态页面抓取器)
```java
public class PageFetcher implements IFetcher {
    // 基于 HttpURLConnection
    - 支持 HTTP/HTTPS 协议
    - 可配置连接和读取超时
    - 支持重试机制
    - 处理重定向和错误状态码
}
```

#### DynamicPageFetcher (动态页面抓取器)
```java
public class DynamicPageFetcher implements IFetcher {
    // 基于 Microsoft Playwright
    - 支持 JavaScript 渲染
    - 可配置浏览器类型和选项
    - 支持等待页面加载完成
    - 处理动态内容和 AJAX 请求
}
```

### 3.3 内容解析器 (ContentParser)

```java
@Component
public class ContentParser {
    // 核心功能
    - HTML 结构解析 (基于 Jsoup)
    - 元数据提取 (标题、描述、关键词)
    - 链接识别和分类 (内部/外部/附件)
    - HTML 到 Markdown 转换
    - 图片和媒体文件处理
}
```

### 3.4 存储管理器 (StorageManager)

```java
@Component
public class StorageManager {
    // 存储策略
    - 按域名和路径组织目录结构
    - 支持多种文件格式保存
    - 元数据文件生成
    - 文件名冲突处理
    - 存储空间管理
}
```

## 4. 数据模型设计

### 4.1 任务实体 (Task Entity)

```java
@Entity
@Table(name = "crawl_task")
public class Task {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;                    // 任务唯一标识
    
    @Column(nullable = false)
    private String url;                 // 起始URL
    
    @Enumerated(EnumType.STRING)
    private TaskStatus status;          // 任务状态
    
    private int progress;               // 进度百分比
    private LocalDateTime startTime;    // 开始时间
    private LocalDateTime endTime;      // 结束时间
    private int maxDepth;              // 最大深度
    private String outputDir;          // 输出目录
    private String allowedDomains;     // 允许域名
    private boolean enableDynamicFetcher; // 启用动态抓取
    // ... 其他配置字段
}
```

### 4.2 任务状态枚举

```java
public enum TaskStatus {
    PENDING,    // 待执行
    RUNNING,    // 执行中
    COMPLETED,  // 已完成
    FAILED,     // 执行失败
    CANCELLED   // 已取消
}
```

### 4.3 页面数据模型 (PageData)

```java
public class PageData {
    private String url;                    // 页面URL
    private String title;                  // 页面标题
    private String htmlContent;            // 原始HTML内容
    private String markdownContent;        // Markdown内容
    private Map<String, String> metaData;  // 元数据
    private List<String> internalLinks;    // 内部链接
    private List<String> externalLinks;    // 外部链接
    private List<PageData> attachments;    // 附件列表
    private LocalDateTime fetchTimestamp;  // 抓取时间戳
    private long contentLength;            // 内容长度
    private int responseCode;              // HTTP响应码
    private String contentType;            // 内容类型
    private boolean processed;             // 是否已处理
}
```

## 5. 并发和性能设计

### 5.1 并发控制策略

#### 线程池配置
```java
// 可配置的线程池大小
private final ExecutorService threadPool = 
    Executors.newFixedThreadPool(threads);

// 每域名并发控制
private final Map<String, Semaphore> domainSemaphores = 
    new ConcurrentHashMap<>();
```

#### URL 去重机制
```java
// 线程安全的访问记录
private final Set<String> visitedUrls = 
    ConcurrentHashMap.newKeySet();
```

### 5.2 性能优化

#### 连接池管理
- HTTP 连接复用
- 连接超时配置
- 最大连接数限制

#### 内存管理
- 大文件流式处理
- 及时释放资源
- 垃圾回收优化

#### 缓存策略
- 页面内容缓存
- DNS 解析缓存
- 静态资源缓存

## 6. 错误处理和容错设计

### 6.1 异常分类

```java
// 自定义异常体系
public class CrawlerException extends Exception {
    // 网络异常
    public static class NetworkException extends CrawlerException {}
    
    // 解析异常
    public static class ParseException extends CrawlerException {}
    
    // 存储异常
    public static class StorageException extends CrawlerException {}
    
    // 配置异常
    public static class ConfigException extends CrawlerException {}
}
```

### 6.2 重试机制

```java
// 可配置的重试策略
@Retryable(
    value = {NetworkException.class},
    maxAttempts = 3,
    backoff = @Backoff(delay = 1000, multiplier = 2)
)
public PageData fetchPage(String url) {
    // 页面抓取逻辑
}
```

### 6.3 熔断保护

- 域名级别的熔断器
- 失败率阈值控制
- 自动恢复机制

## 7. 安全设计

### 7.1 输入验证
- URL 格式验证
- 参数范围检查
- SQL 注入防护
- XSS 攻击防护

### 7.2 访问控制
- API 密钥认证
- 请求频率限制
- IP 白名单机制

### 7.3 数据安全
- 敏感信息脱敏
- 数据传输加密
- 存储权限控制

## 8. 监控和运维

### 8.1 指标收集

```java
// Prometheus 指标定义
@Component
public class MetricsExporter {
    private final Counter crawledPagesTotal;
    private final Histogram crawlDuration;
    private final Gauge activeTasks;
    // ... 其他指标
}
```

### 8.2 健康检查

```java
@Component
public class CrawlerHealthIndicator implements HealthIndicator {
    @Override
    public Health health() {
        // 检查爬虫组件状态
        // 检查数据库连接
        // 检查磁盘空间
        return Health.up().build();
    }
}
```

### 8.3 日志管理

```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/crawler.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/crawler.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>
</configuration>
```

## 9. 扩展性设计

### 9.1 插件化架构
- 抓取器插件接口
- 解析器插件接口
- 存储器插件接口

### 9.2 配置化扩展
- 自定义解析规则
- 可配置的过滤器
- 动态加载配置

### 9.3 分布式扩展
- 任务队列分离
- 多实例部署
- 负载均衡支持

## 10. 部署架构

### 10.1 单机部署
```
┌─────────────────┐
│   Spring Boot   │
│   Application   │
├─────────────────┤
│   H2 Database   │
├─────────────────┤
│  File Storage   │
└─────────────────┘
```

### 10.2 集群部署
```
┌─────────────┐    ┌─────────────┐
│ Crawler #1  │    │ Crawler #2  │
└─────────────┘    └─────────────┘
       │                  │
       └──────────────────┘
              │
    ┌─────────────────┐
    │ Shared Database │
    └─────────────────┘
```

---

**文档版本**: 1.0  
**最后更新**: 2025-06-23  
**维护者**: 开发团队
