# 技术栈

## 核心语言与框架
- **Java 22+ **: 项目主要开发语言。
- **Maven**: 用于项目构建和依赖管理。

## 网页抓取
- **Playwright**: 用于处理动态渲染的网页（JavaScript密集型网站）。能够模拟浏览器行为，获取完整的页面HTML。
- **HttpURLConnection**: (现有实现中) 用于抓取静态网页内容。

## 其他
- **SLF4J**: 用于日志记录。
- **JUnit**: 用于单元测试。

## 核心语言与框架
-   **Java**: 主要开发语言。
    -   版本：待确认（通常与 `pom.xml` 或项目设置一致，例如 Java 21+ ）。
-   **Maven**: 项目构建和依赖管理。

## 测试技术
-   **JUnit 5**: 单元测试框架。
-   **Mockito**: Mocking框架，用于隔离测试单元。
-   **Testcontainers**: 用于集成测试，提供轻量级、一次性的常见数据库、Selenium Web浏览器或任何其他可以在Docker容器中运行的实例。
-   **JMH (Java Microbenchmark Harness)**: 用于Java代码的基准测试。

## HTML 解析
-   **Jsoup**: 用于解析HTML文档，提取数据（推测，常见的Java HTML解析库）。

## 日志
-   **SLF4J + Logback/Log4j2**: (推测，常见的Java日志框架组合)。

## 命令行处理
-   **Apache Commons CLI / Picocli**: (推测，常见的Java CLI库)。

## 其他
-   **Jackson/Gson**: (推测，用于处理JSON，例如 `crawl_report.json`)。

## 动态页面渲染与浏览器自动化

-   **技术选型**: Playwright for Java
    -   **选型理由**:
        -   **现代API**: Playwright 提供简洁、现代的API，易于学习和使用，能够提升开发效率。
        -   **Java SDK**: 官方提供成熟的 Java SDK，与本项目现有技术栈（Java）完美集成。
        -   **跨浏览器支持**: 支持 Chromium, Firefox, 和 WebKit，确保了广泛的网站兼容性和测试覆盖。
        -   **自动等待**: 内置智能的自动等待机制，能够自动等待元素加载完成或JavaScript执行完毕，显著简化了异步页面处理的复杂性。
        -   **性能与可靠性**: 相较于传统方案（如Selenium），Playwright 通常具有更好的性能和更高的执行可靠性。
        -   **网络控制**: 提供强大的网络请求拦截、修改和模拟功能，便于处理复杂场景和进行精细化测试。
    -   **版本**: (建议调研并使用最新的稳定版本，例如 `1.4x.x` 系列。请在添加依赖时确认。)
    -   **主要依赖 (Maven)**:
        ```xml
        <dependency>
            <groupId>com.microsoft.playwright</groupId>
            <artifactId>playwright</artifactId>
            <version>YOUR_CHOSEN_VERSION</version> <!-- 请替换为选定的最新稳定版本 -->
        </dependency>
        ```

    -   **核心用途**:
        -   获取由客户端JavaScript动态生成和渲染的完整HTML内容。
        -   作为新的页面获取机制，处理那些需要执行JavaScript才能获取完整内容的URL。
        -   为未来可能需要的更复杂浏览器交互（如用户行为模拟、截图等）提供基础。
