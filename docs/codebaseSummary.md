# 代码库概要

## 项目结构
项目遵循标准的Maven目录结构。
- `src/main/java`: 存放核心业务代码。
  - `com.talkweb.ai.crawler`: 项目根包。
    - `fetcher`: 包含页面抓取逻辑。
    - `model`: 数据模型（如 `PageData`）。
    - `...` (其他待补充的模块)
- `src/test/java`: 存放测试代码。
- `docs`: 存放项目文档。

## 关键组件
### Fetcher (抓取器)
- **`DynamicPageFetcher.java`**: 使用 **Playwright** 实现，能够抓取需要JavaScript渲染的动态页面。这是当前推荐使用的抓取器。
- **`PageFetcher.java`**: 使用标准的 `HttpURLConnection` 实现，仅适用于抓取静态HTML内容。

## 数据流
1. 爬虫核心模块接收一个URL。
2. 调用 `Fetcher` (抓取器) 获取页面内容，并封装成 `PageData` 对象。
3. (待实现) `Parser` (解析器) 处理 `PageData` 对象，提取所需信息。
4. (待实现) `Storage` (存储器) 将处理后的数据持久化。

## 最新变更
- 在 `pom.xml` 中添加了 `playwright` 依赖。
- 确认项目中已存在 `DynamicPageFetcher`，无需重新开发。

## 项目结构概览
本项目是一个Java Maven项目，旨在实现一个网站爬虫，将抓取的网页内容转换为Markdown格式。主要源代码位于 `src/main/java/com/talkweb/ai/crawler`。

## 关键组件及其交互

### 新增: `fetcher.DynamicPageFetcher` (动态页面获取器)
- **职责**:
  - 使用 Playwright for Java 启动和管理无头浏览器实例。
  - 接收一个 URL，导航到该页面，并等待 JavaScript 执行和内容渲染完成。
  - 提取并返回渲染后的完整 HTML 字符串。
  - 负责处理浏览器相关的超时、错误和资源清理。
- **交互**:
  - **被 `CrawlCoordinator` 调用**: 当配置启用动态渲染时，`CrawlCoordinator` 委托 `DynamicPageFetcher` 来获取 HTML。
  - **输出给 [ContentParser](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/parser/ContentParser.java:26:0-259:1)**: 其返回的 HTML 字符串将作为输入传递给 [ContentParser](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/parser/ContentParser.java:26:0-259:1) 进行解析。
- **说明**: 这是一个可选的页面获取器，用于处理需要客户端渲染的复杂网页。默认情况下不启用。
### `CrawlCoordinator`
-   **职责**: 协调整个抓取过程，管理URL队列，调度抓取任务，并控制抓取深度和范围。
-   **交互**:
    -   使用 `PageFetcher` 获取页面内容。
    -   使用 `ContentParser` 解析页面内容。
    -   使用 `HtmlToMarkdownConverter` 将HTML转换为Markdown。
    -   使用 `FileDownloader` 下载附件。
    -   使用 `StorageManager` 保存Markdown文件和附件。
    - **(变更)** 调用 `DynamicPageFetcher` 获取页面内容，替代原有的直接HTTP请求方式。
    - **(变更)** 根据配置决定调用 `PageFetcher` (默认) 或 `DynamicPageFetcher` (当启用动态渲染时) 来获取页面内容。

### `fetcher.PageFetcher` (页面获取器)
- **职责**:
  - 通过标准的 HTTP/HTTPS 请求从给定的URL获取原始HTML内容。
  - 处理基本的网络连接、超时和HTTP错误。
- **交互**:
  - **被 `CrawlCoordinator` 调用**: 作为默认的页面获取机制。
  - **输出给 [ContentParser](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/parser/ContentParser.java:26:0-259:1)**: 其返回的 HTML 字符串将作为输入传递给 [ContentParser](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/parser/ContentParser.java:26:0-259:1) 进行解析。
- **说明**: 这是项目默认的页面获取方式，适用于大多数静态或服务端渲染的网页，速度较快。


### `ContentParser`
-   **职责**: 解析HTML内容，提取元数据（如标题、描述、关键词）、正文内容，并识别页面中的链接（包括内部链接、外部链接和附件链接）。
-   **交互**:
    -   被 `CrawlCoordinator` 调用，接收原始HTML内容。
    -   可能使用HTML解析库（如Jsoup，根据 `techStack.md` 推测）。

### `HtmlToMarkdownConverter`
-   **职责**: 将解析后的HTML结构（或特定部分）转换为Markdown格式。处理标题、段落、列表、表格、图片等元素的转换。
-   **交互**:
    -   被 `CrawlCoordinator` 或 `ContentParser` 调用。

### `FileDownloader`
-   **职责**: 负责下载在页面中识别到的附件（如PDF, DOC, TXT等）。
-   **交互**:
    -   被 `CrawlCoordinator` 调用，接收附件URL和下载配置。
    -   与 `StorageManager` 协作，将下载的文件保存到指定位置。

### `StorageManager`
-   **职责**: 管理抓取结果的存储，包括Markdown文件和下载的附件。确保文件按URL结构层次化存储。
-   **交互**:
    -   被 `CrawlCoordinator` 和 `FileDownloader` 调用。

### `ConfigLoader`
-   **职责**: 加载和管理项目配置，包括命令行参数、配置文件和默认值。

## 数据流
1.  **配置读取**: 系统启动时，`ConfigLoader` 加载配置，其中包括是否为当前抓取任务启用动态页面渲染的选项。
2.  **页面获取决策**: `CrawlCoordinator` 将待抓取 URL 交给页面获取模块。
    *   **如果动态渲染未启用 (默认)**: `PageFetcher` 通过标准 HTTP 请求获取页面 HTML。
    *   **如果动态渲染已启用**: `DynamicPageFetcher` 使用 Playwright 打开 URL，执行 JavaScript，获取渲染后的完整 HTML。
3.  **内容解析**: 获取到的 HTML 字符串（无论来源）返回给 `CrawlCoordinator`，后者再将其交给 [ContentParser](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/parser/ContentParser.java:26:0-259:1)。
4.  **数据提取**: [ContentParser](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/parser/ContentParser.java:26:0-259:1) 解析 HTML，提取标题、正文（转换为Markdown）、内链、外链和附件链接，封装成 `PageData` 对象。
5.  **调度与存储**: `CrawlCoordinator` 接收 `PageData` 对象。
    -   将新的内部链接放入抓取队列。
    -   将 `PageData` 中的Markdown内容交由 `StorageManager` 保存到文件系统。
    -   将附件信息交由 [FileDownloader](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/downloader/FileDownloader.java:20:0-147:1) 处理。
6.  **附件下载**: [FileDownloader](cci:2://file:///Users/<USER>/Downloads/%E9%87%8D%E5%BA%86%E5%9B%AD%E5%8C%BA/src/search/crawler/src/main/java/com/talkweb/ai/crawler/downloader/FileDownloader.java:20:0-147:1) 根据附件信息下载文件，并由 `StorageManager` 保存到指定目录。
7.  **循环**: `CrawlCoordinator` 从队列中取出下一个URL，重复步骤2-6，直到队列为空或达到抓取限制。

## 外部依赖项
-   参考 `techStack.md` 和 `pom.xml` 文件。

## 近期重大变更
- **引入可选的动态页面渲染能力**:
  - **变更**: 为项目增加了处理 JavaScript 渲染页面的能力，作为一项可配置功能。
  - **技术选型**: 动态渲染采用 **Playwright for Java**。
  - **架构影响**:
    - 新增 `DynamicPageFetcher` 组件，负责基于浏览器的页面获取。
    - `PageFetcher` 继续作为默认的、基于HTTP请求的快速获取器。
    - `CrawlCoordinator` 的页面获取逻辑将根据配置动态选择使用 `PageFetcher` 或 `DynamicPageFetcher`。
    - 项目将新增 Playwright 的 Maven 依赖。
    - 需要添加新的配置项来控制是否启用动态渲染。
## 用户反馈整合及其对开发的影响
-   (待补充)
