# AI 网页爬虫系统 - 部署运维文档

## 1. 环境要求

### 1.1 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+), macOS, Windows 10+
- **CPU**: 2核心以上 (推荐4核心)
- **内存**: 4GB 以上 (推荐8GB)
- **磁盘**: 20GB 以上可用空间
- **网络**: 稳定的互联网连接

### 1.2 软件依赖
- **Java**: OpenJDK 21 或更高版本
- **Maven**: 3.6.0 或更高版本
- **Git**: 用于代码管理
- **Docker**: 可选，用于容器化部署

### 1.3 浏览器依赖 (动态抓取)
如果启用动态页面抓取功能，Playwright 会自动下载所需的浏览器：
- Chromium
- Firefox
- WebKit (Safari)

## 2. 安装部署

### 2.1 源码部署

#### 步骤1: 获取源码
```bash
# 克隆项目
git clone <repository-url>
cd crawler

# 检查Java版本
java -version
# 应显示 Java 21 或更高版本
```

#### 步骤2: 编译项目
```bash
# 清理并编译
mvn clean compile

# 运行测试 (可选)
mvn test

# 打包应用
mvn clean package -DskipTests
```

#### 步骤3: 配置应用
```bash
# 复制配置文件模板
cp src/main/resources/application.properties.example application.properties

# 编辑配置文件
vim application.properties
```

#### 步骤4: 启动应用
```bash
# 开发模式启动
mvn spring-boot:run

# 或使用打包后的JAR
java -jar target/crawler-1.0-SNAPSHOT.jar
```

### 2.2 Docker 部署

#### 创建 Dockerfile
```dockerfile
FROM openjdk:21-jre-slim

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 复制应用文件
COPY target/crawler-1.0-SNAPSHOT.jar app.jar
COPY application.properties application.properties

# 创建数据目录
RUN mkdir -p /app/data /app/logs /app/output

# 暴露端口
EXPOSE 8080 9090

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g"
ENV SPRING_PROFILES_ACTIVE=docker

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 构建和运行
```bash
# 构建镜像
docker build -t ai-crawler:latest .

# 运行容器
docker run -d \
  --name ai-crawler \
  -p 8080:8080 \
  -p 9090:9090 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/output:/app/output \
  ai-crawler:latest
```

### 2.3 Docker Compose 部署

#### docker-compose.yml
```yaml
version: '3.8'

services:
  crawler:
    build: .
    container_name: ai-crawler
    ports:
      - "8080:8080"
      - "9090:9090"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./output:/app/output
      - ./application.properties:/app/application.properties
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - JAVA_OPTS=-Xmx2g -Xms1g
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  prometheus:
    image: prom/prometheus:latest
    container_name: crawler-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: crawler-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    restart: unless-stopped

volumes:
  grafana-storage:
```

#### 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f crawler
```

## 3. 配置管理

### 3.1 应用配置 (application.properties)

```properties
# 服务器配置
server.port=8080
server.servlet.context-path=/

# 数据库配置
spring.datasource.url=jdbc:h2:file:./data/crawlerdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# 日志配置
logging.level.com.talkweb.ai.crawler=INFO
logging.file.name=logs/crawler.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# 爬虫默认配置
crawler.default.max-depth=3
crawler.default.threads=4
crawler.default.connection-timeout=10000
crawler.default.read-timeout=30000
crawler.default.max-connections-per-domain=5
crawler.default.max-retries=3
crawler.default.enable-dynamic-fetcher=false
crawler.default.download-attachments=false

# 监控配置
management.endpoints.web.exposure.include=health,info,prometheus,metrics
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# 安全配置
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN
```

### 3.2 生产环境配置 (application-prod.properties)

```properties
# 生产环境数据库配置
spring.datasource.url=jdbc:h2:file:/app/data/crawlerdb
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5

# 生产环境日志配置
logging.level.root=WARN
logging.level.com.talkweb.ai.crawler=INFO
logging.file.name=/app/logs/crawler.log
logging.logback.rollingpolicy.max-file-size=100MB
logging.logback.rollingpolicy.max-history=30

# 性能优化配置
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# 安全配置
spring.security.user.password=${ADMIN_PASSWORD:admin123}
```

### 3.3 环境变量配置

```bash
# 数据库配置
export DB_URL="jdbc:h2:file:/app/data/crawlerdb"
export DB_USERNAME="sa"
export DB_PASSWORD=""

# 应用配置
export SERVER_PORT=8080
export ADMIN_PASSWORD="your-secure-password"
export JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC"

# 爬虫配置
export CRAWLER_DEFAULT_THREADS=8
export CRAWLER_DEFAULT_MAX_DEPTH=5
```

## 4. 监控配置

### 4.1 Prometheus 配置 (prometheus.yml)

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'crawler'
    static_configs:
      - targets: ['crawler:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
```

### 4.2 Grafana 仪表板配置

创建 `monitoring/grafana/dashboards/crawler-dashboard.json`:

```json
{
  "dashboard": {
    "title": "AI Crawler Dashboard",
    "panels": [
      {
        "title": "Active Tasks",
        "type": "stat",
        "targets": [
          {
            "expr": "crawler_active_tasks",
            "legendFormat": "Active Tasks"
          }
        ]
      },
      {
        "title": "Pages Crawled Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(crawler_pages_crawled_total[5m])",
            "legendFormat": "Pages/sec"
          }
        ]
      },
      {
        "title": "Task Duration",
        "type": "heatmap",
        "targets": [
          {
            "expr": "crawler_task_duration_seconds_bucket",
            "legendFormat": "Duration"
          }
        ]
      }
    ]
  }
}
```

## 5. 日志管理

### 5.1 日志配置 (logback-spring.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="!prod">
        <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
        <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/crawler.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/crawler.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/errors.log</file>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/errors.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>90</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <logger name="com.talkweb.ai.crawler" level="INFO"/>
        <root level="WARN">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>
```

### 5.2 日志轮转和清理

```bash
#!/bin/bash
# log-cleanup.sh - 日志清理脚本

LOG_DIR="/app/logs"
RETENTION_DAYS=30

# 清理超过30天的日志文件
find $LOG_DIR -name "*.log.*" -type f -mtime +$RETENTION_DAYS -delete

# 压缩7天前的日志文件
find $LOG_DIR -name "*.log.*" -type f -mtime +7 ! -name "*.gz" -exec gzip {} \;

echo "日志清理完成: $(date)"
```

## 6. 备份和恢复

### 6.1 数据备份脚本

```bash
#!/bin/bash
# backup.sh - 数据备份脚本

BACKUP_DIR="/backup"
DATA_DIR="/app/data"
OUTPUT_DIR="/app/output"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp -r $DATA_DIR $BACKUP_DIR/data_$DATE

# 备份输出文件
tar -czf $BACKUP_DIR/output_$DATE.tar.gz -C $OUTPUT_DIR .

# 清理超过7天的备份
find $BACKUP_DIR -type f -mtime +7 -delete

echo "备份完成: $DATE"
```

### 6.2 数据恢复

```bash
#!/bin/bash
# restore.sh - 数据恢复脚本

BACKUP_FILE=$1
RESTORE_DIR="/app"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <备份文件>"
    exit 1
fi

# 停止应用
docker-compose stop crawler

# 恢复数据
tar -xzf $BACKUP_FILE -C $RESTORE_DIR

# 启动应用
docker-compose start crawler

echo "恢复完成"
```

## 7. 性能调优

### 7.1 JVM 参数优化

```bash
# 生产环境JVM参数
JAVA_OPTS="-Xmx4g -Xms2g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/app/logs/gc.log"
```

### 7.2 数据库优化

```properties
# 连接池优化
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA优化
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
```

### 7.3 爬虫性能优化

```properties
# 并发优化
crawler.default.threads=8
crawler.default.max-connections-per-domain=10

# 超时优化
crawler.default.connection-timeout=5000
crawler.default.read-timeout=15000

# 内存优化
crawler.default.max-file-size=50MB
```

## 8. 故障排查

### 8.1 常见问题

#### 应用启动失败
```bash
# 检查Java版本
java -version

# 检查端口占用
netstat -tlnp | grep 8080

# 查看启动日志
tail -f logs/crawler.log
```

#### 内存不足
```bash
# 查看内存使用
free -h

# 查看JVM内存
jstat -gc <pid>

# 调整JVM参数
export JAVA_OPTS="-Xmx2g -Xms1g"
```

#### 数据库连接问题
```bash
# 检查数据库文件
ls -la data/

# 检查数据库连接
curl http://localhost:8080/actuator/health
```

### 8.2 监控告警

#### Prometheus 告警规则
```yaml
groups:
  - name: crawler.rules
    rules:
      - alert: CrawlerDown
        expr: up{job="crawler"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Crawler service is down"

      - alert: HighMemoryUsage
        expr: jvm_memory_used_bytes / jvm_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"

      - alert: TooManyFailedTasks
        expr: rate(crawler_tasks_failed_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High task failure rate"
```

## 9. 安全配置

### 9.1 网络安全
```bash
# 防火墙配置
ufw allow 8080/tcp
ufw allow 9090/tcp
ufw enable
```

### 9.2 应用安全
```properties
# HTTPS配置
server.ssl.enabled=true
server.ssl.key-store=classpath:keystore.p12
server.ssl.key-store-password=password
server.ssl.key-store-type=PKCS12

# 安全头配置
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true
```

---

**文档版本**: 1.0  
**最后更新**: 2025-06-23  
**维护者**: 运维团队
