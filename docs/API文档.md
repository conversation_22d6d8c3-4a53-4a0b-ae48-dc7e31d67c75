# AI 网页爬虫系统 - API 文档

## 概述

本文档描述了 AI 网页爬虫系统提供的 REST API 接口。所有 API 都遵循 RESTful 设计原则，使用 JSON 格式进行数据交换。

### 基础信息
- **基础URL**: `http://localhost:8080`
- **API版本**: v1
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 认证方式
API 使用基于 HTTP Basic Auth 的简单认证机制（开发环境）。

## 任务管理 API

### 1. 创建爬取任务

创建一个新的网页爬取任务。

**请求**
```http
POST /api/tasks
Content-Type: application/json

{
  "url": "https://example.com",
  "maxDepth": 3,
  "outputDir": "/path/to/output",
  "allowedDomains": ["example.com", "subdomain.example.com"],
  "maxConnectionsPerDomain": 5,
  "enableDynamicFetcher": true,
  "downloadAttachments": false,
  "connectionTimeout": 10000,
  "readTimeout": 30000,
  "maxRetries": 3
}
```

**请求参数说明**

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| url | string | 是 | - | 起始URL，必须是有效的HTTP/HTTPS地址 |
| maxDepth | integer | 否 | 3 | 最大递归深度，范围：1-10 |
| outputDir | string | 否 | "./output" | 输出目录路径 |
| allowedDomains | array | 否 | [] | 允许爬取的域名列表，空数组表示不限制 |
| maxConnectionsPerDomain | integer | 否 | 5 | 每个域名的最大并发连接数 |
| enableDynamicFetcher | boolean | 否 | false | 是否启用动态页面抓取（JavaScript渲染） |
| downloadAttachments | boolean | 否 | false | 是否下载页面中的附件 |
| connectionTimeout | integer | 否 | 10000 | 连接超时时间（毫秒） |
| readTimeout | integer | 否 | 30000 | 读取超时时间（毫秒） |
| maxRetries | integer | 否 | 3 | 最大重试次数 |

**响应**
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "url": "https://example.com",
  "status": "PENDING",
  "progress": 0,
  "startTime": "2025-06-23T10:00:00Z",
  "endTime": null,
  "maxDepth": 3,
  "outputDir": "/path/to/output",
  "allowedDomains": ["example.com"],
  "enableDynamicFetcher": true,
  "downloadAttachments": false,
  "createdAt": "2025-06-23T10:00:00Z",
  "updatedAt": "2025-06-23T10:00:00Z"
}
```

**错误响应**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "INVALID_INPUT",
  "message": "URL格式无效",
  "details": {
    "field": "url",
    "value": "invalid-url",
    "reason": "必须是有效的HTTP或HTTPS地址"
  },
  "timestamp": "2025-06-23T10:00:00Z"
}
```

### 2. 查询任务详情

根据任务ID查询特定任务的详细信息。

**请求**
```http
GET /api/tasks/{taskId}
```

**路径参数**
- `taskId`: 任务的唯一标识符（UUID格式）

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "url": "https://example.com",
  "status": "RUNNING",
  "progress": 45,
  "startTime": "2025-06-23T10:00:00Z",
  "endTime": null,
  "maxDepth": 3,
  "outputDir": "/path/to/output",
  "allowedDomains": ["example.com"],
  "enableDynamicFetcher": true,
  "downloadAttachments": false,
  "statistics": {
    "totalUrls": 150,
    "crawledUrls": 68,
    "failedUrls": 2,
    "downloadedFiles": 12,
    "totalSize": "2.5MB"
  },
  "createdAt": "2025-06-23T10:00:00Z",
  "updatedAt": "2025-06-23T10:15:30Z"
}
```

**错误响应**
```http
HTTP/1.1 404 Not Found
Content-Type: application/json

{
  "error": "TASK_NOT_FOUND",
  "message": "指定的任务不存在",
  "taskId": "550e8400-e29b-41d4-a716-************",
  "timestamp": "2025-06-23T10:00:00Z"
}
```

### 3. 获取任务列表

获取所有任务的分页列表，支持状态过滤和排序。

**请求**
```http
GET /api/tasks?page=0&size=10&status=RUNNING&sort=createdAt,desc
```

**查询参数**

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 0 | 页码，从0开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| status | string | 否 | - | 任务状态过滤：PENDING, RUNNING, COMPLETED, FAILED, CANCELLED |
| sort | string | 否 | createdAt,desc | 排序字段和方向 |

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "content": [
    {
      "taskId": "550e8400-e29b-41d4-a716-************",
      "url": "https://example.com",
      "status": "RUNNING",
      "progress": 45,
      "startTime": "2025-06-23T10:00:00Z",
      "createdAt": "2025-06-23T10:00:00Z"
    }
  ],
  "pageable": {
    "page": 0,
    "size": 10,
    "sort": "createdAt,desc"
  },
  "totalElements": 25,
  "totalPages": 3,
  "first": true,
  "last": false,
  "numberOfElements": 10
}
```

### 4. 取消任务

取消正在执行或等待执行的任务。

**请求**
```http
POST /api/tasks/{taskId}/cancel
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "url": "https://example.com",
  "status": "CANCELLED",
  "progress": 45,
  "startTime": "2025-06-23T10:00:00Z",
  "endTime": "2025-06-23T10:15:30Z",
  "message": "任务已成功取消"
}
```

### 5. 重试任务

重新执行失败的任务。

**请求**
```http
POST /api/tasks/{taskId}/retry
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "url": "https://example.com",
  "status": "PENDING",
  "progress": 0,
  "startTime": null,
  "endTime": null,
  "message": "任务已重新排队执行"
}
```

## 任务状态说明

| 状态 | 说明 |
|------|------|
| PENDING | 任务已创建，等待执行 |
| RUNNING | 任务正在执行中 |
| COMPLETED | 任务已成功完成 |
| FAILED | 任务执行失败 |
| CANCELLED | 任务已被取消 |

## 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| INVALID_INPUT | 400 | 请求参数无效 |
| TASK_NOT_FOUND | 404 | 任务不存在 |
| TASK_ALREADY_RUNNING | 409 | 任务已在运行中 |
| TASK_CANNOT_BE_CANCELLED | 409 | 任务无法取消 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 监控和管理 API

### 1. 健康检查

检查应用程序的健康状态。

**请求**
```http
GET /actuator/health
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": "UP",
  "components": {
    "db": {
      "status": "UP",
      "details": {
        "database": "H2",
        "validationQuery": "isValid()"
      }
    },
    "diskSpace": {
      "status": "UP",
      "details": {
        "total": 499963174912,
        "free": 91943821312,
        "threshold": 10485760,
        "exists": true
      }
    },
    "crawler": {
      "status": "UP",
      "details": {
        "activeTasks": 2,
        "totalTasks": 15,
        "threadPoolSize": 4
      }
    }
  }
}
```

### 2. 应用信息

获取应用程序的基本信息。

**请求**
```http
GET /actuator/info
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "app": {
    "name": "AI Web Crawler",
    "version": "1.0-SNAPSHOT",
    "description": "智能网页爬虫系统"
  },
  "build": {
    "time": "2025-06-23T08:00:00Z",
    "version": "1.0-SNAPSHOT"
  },
  "git": {
    "branch": "main",
    "commit": {
      "id": "abc123",
      "time": "2025-06-23T07:30:00Z"
    }
  }
}
```

### 3. 指标数据

获取 Prometheus 格式的监控指标。

**请求**
```http
GET /actuator/prometheus
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: text/plain

# HELP crawler_pages_crawled_total Total number of pages crawled
# TYPE crawler_pages_crawled_total counter
crawler_pages_crawled_total 1234.0

# HELP crawler_active_tasks Current number of active tasks
# TYPE crawler_active_tasks gauge
crawler_active_tasks 3.0

# HELP crawler_task_duration_seconds Time spent crawling tasks
# TYPE crawler_task_duration_seconds histogram
crawler_task_duration_seconds_bucket{le="1.0"} 45.0
crawler_task_duration_seconds_bucket{le="5.0"} 123.0
crawler_task_duration_seconds_bucket{le="10.0"} 156.0
crawler_task_duration_seconds_bucket{le="+Inf"} 167.0
crawler_task_duration_seconds_sum 892.5
crawler_task_duration_seconds_count 167.0
```

## 使用示例

### JavaScript/Node.js 示例

```javascript
const axios = require('axios');

// 创建爬取任务
async function createCrawlTask() {
  try {
    const response = await axios.post('http://localhost:8080/api/tasks', {
      url: 'https://example.com',
      maxDepth: 2,
      enableDynamicFetcher: true,
      allowedDomains: ['example.com']
    });
    
    console.log('任务创建成功:', response.data);
    return response.data.taskId;
  } catch (error) {
    console.error('创建任务失败:', error.response.data);
  }
}

// 查询任务状态
async function checkTaskStatus(taskId) {
  try {
    const response = await axios.get(`http://localhost:8080/api/tasks/${taskId}`);
    console.log('任务状态:', response.data);
    return response.data;
  } catch (error) {
    console.error('查询失败:', error.response.data);
  }
}

// 使用示例
(async () => {
  const taskId = await createCrawlTask();
  if (taskId) {
    // 轮询任务状态
    const interval = setInterval(async () => {
      const task = await checkTaskStatus(taskId);
      if (task && ['COMPLETED', 'FAILED', 'CANCELLED'].includes(task.status)) {
        clearInterval(interval);
        console.log('任务完成:', task);
      }
    }, 5000);
  }
})();
```

### Python 示例

```python
import requests
import time

class CrawlerClient:
    def __init__(self, base_url='http://localhost:8080'):
        self.base_url = base_url
    
    def create_task(self, url, **kwargs):
        """创建爬取任务"""
        data = {'url': url, **kwargs}
        response = requests.post(f'{self.base_url}/api/tasks', json=data)
        response.raise_for_status()
        return response.json()
    
    def get_task(self, task_id):
        """获取任务详情"""
        response = requests.get(f'{self.base_url}/api/tasks/{task_id}')
        response.raise_for_status()
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=300):
        """等待任务完成"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            task = self.get_task(task_id)
            if task['status'] in ['COMPLETED', 'FAILED', 'CANCELLED']:
                return task
            time.sleep(5)
        raise TimeoutError('任务执行超时')

# 使用示例
client = CrawlerClient()

# 创建任务
task = client.create_task(
    url='https://example.com',
    maxDepth=2,
    enableDynamicFetcher=True,
    allowedDomains=['example.com']
)

print(f"任务已创建: {task['taskId']}")

# 等待完成
try:
    completed_task = client.wait_for_completion(task['taskId'])
    print(f"任务完成: {completed_task['status']}")
except TimeoutError as e:
    print(f"任务超时: {e}")
```

## 最佳实践

### 1. 任务配置建议
- **maxDepth**: 建议不超过5，避免过深递归
- **allowedDomains**: 明确指定允许域名，避免爬取无关内容
- **enableDynamicFetcher**: 仅在需要JavaScript渲染时启用
- **connectionTimeout**: 根据网络环境调整，建议5-30秒

### 2. 错误处理
- 始终检查HTTP状态码
- 解析错误响应中的详细信息
- 实现适当的重试机制

### 3. 性能优化
- 合理设置并发数，避免对目标网站造成压力
- 使用分页查询大量任务
- 定期清理已完成的任务

---

**文档版本**: 1.0  
**最后更新**: 2025-06-23  
**在线文档**: http://localhost:8080/swagger-ui.html
