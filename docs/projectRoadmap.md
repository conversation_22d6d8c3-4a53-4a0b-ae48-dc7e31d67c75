# 项目路线图 - AI数据采集爬虫

## 核心目标

- [X] 初始化项目结构和基础依赖
- [ ] 实现一个能够抓取静态网页内容的爬虫
- [X] **集成动态内容抓取能力，以处理JavaScript渲染的页面**
- [ ] 实现内容解析模块，从HTML中提取关键信息
- [ ] 实现数据存储模块，将抓取和解析后的数据持久化
- [ ] 构建灵活的爬虫调度与控制核心
- [ ] 增加错误处理、重试和日志记录机制
- [ ] 编写单元测试和端到端测试，确保系统稳定性

## 未来可扩展性

- 支持分布式爬取
- 提供API接口用于任务管理和数据查询


## 核心目标

- [X] 初始化项目结构和基础依赖
- [ ] 实现一个能够抓取静态网页内容的爬虫
- [X] **集成动态内容抓取能力，以处理JavaScript渲染的页面** (已发现现有实现)
- [ ] 实现内容解析模块，从HTML中提取关键信息
- [ ] 实现数据存储模块，将抓取和解析后的数据持久化
- [ ] 构建灵活的爬虫调度与控制核心
- [ ] 增加错误处理、重试和日志记录机制
- [ ] 编写单元测试和端到端测试，确保系统稳定性

## 未来可扩展性

- 支持分布式爬取
- 提供API接口用于任务管理和数据查询


## 核心目标

- [X] 初始化项目结构和基础依赖
- [ ] 实现一个能够抓取静态网页内容的爬虫
- [X] **集成动态内容抓取能力，以处理JavaScript渲染的页面** (已发现现有实现)
- [ ] 实现内容解析模块，从HTML中提取关键信息
- [ ] 实现数据存储模块，将抓取和解析后的数据持久化
- [ ] 构建灵活的爬虫调度与控制核心
- [ ] 增加错误处理、重试和日志记录机制
- [ ] 编写单元测试和端到端测试，确保系统稳定性

## 未来可扩展性

- 支持分布式爬取
- 提供API接口用于任务管理和数据查询
- 集成更多类型的内容解析器（如PDF、图片文字识别）

## 项目概述

本路线图根据设计文档 `web_to_md_crawler_design2.md` 拆解而成，列出了实现网站爬虫的待完成任务清单。该工具旨在递归抓取网页内容，便于大语言模型处理和RAG应用。

## 任务清单

### 阶段 1：框架搭建、配置加载（第1周）

- [X] 搭建项目主程序框架，确保可运行。
- [X] 实现命令行参数（CLI）支持，包括 `--url`, `--max-depth`, `--allowed-domains`, `--threads`, `--output`, `--config`。
- [X] 实现配置文件（`.properties` 格式）加载功能，支持默认值。
- [X] 完成配置优先级逻辑：命令行参数 > 配置文件 > 默认值。

### 阶段 2：抓取 （第2周）
- [X] 实现图片处理逻辑，保留图片说明（alt/title）与原始URL。
- [X] 完成基本的内容解析功能（`ContentParser`），提取meta信息、标题、正文内容。
- [ ] **需求变更：** 实现动态页面渲染能力，以处理JavaScript加载的内容。
- [ ] **技术选型：** 评估并选择适合的无头浏览器技术（如Playwright, Selenium, HtmlUnit）。
- [ ] **实现 `DynamicPageFetcher`：** 开发新组件，负责使用无头浏览器加载URL并返回渲染后的HTML。
- [ ] **集成 `DynamicPageFetcher`：** 修改 `CrawlCoordinator` 以使用新的获取器。
- [ ] 原 `PageFetcher` 职责调整/废弃。
- [ ] ~~实现单页抓取功能（`PageFetcher`），支持基本的URL内容请求。~~ (被 `DynamicPageFetcher`替代)

### 阶段 3：递归调度 + 附件处理（第3周）

- [X] 实现递归抓取逻辑（`CrawlCoordinator`），遵循最大深度限制。
- [X] 实现URL去重机制，避免重复抓取。
- [X] 实现附件处理功能（`FileDownloader`），支持下载word/txt/pdf文件。
- [X] 在Markdown中保留附件说明、原始链接及本地文件路径。
- [X] 处理非本域名外链，标记为外链链接信息。

### 阶段 4：完善存储 + 并发控制（第4周）

- [X] 实现存储管理功能（`StorageManager`），按照URL路径结构层次化保存Markdown文件。
- [X] 实现附件及资源文件的统一目录结构存储。
- [X] 实现并发控制，使用固定线程池（`ExecutorService`）支持多线程抓取。
- [X] 优化每域名最大连接数，防止网络阻塞。

### 阶段 5：日志与测试（第5周）

- [X] 实现日志系统，支持INFO/WARN/ERROR级别，输出结构化日志（如JSON格式）。
- [X] 实现异常链接清单记录（`errors.log`）。
- [X] 实现抓取统计与报告生成（`CrawlReporter`），输出 `crawl_report.json`。
- [X] 编写单元测试（JUnit 5, Mockito），覆盖核心算法、工具类、配置加载。
- [X] 进行集成测试（Testcontainers），验证模块间交互。
- [X] 进行端到端测试，针对特定网站抓取，验证输出结构完整性。
- [X] 进行性能测试（JMH），基准测试关键路径性能。
- [ ] 为 `DynamicPageFetcher` 编写单元测试和集成测试。

### 其他非功能性需求任务

- [X] 确保配置灵活性（NF-1），参数支持CLI、配置文件加载及默认值。
- [X] 确保可移植性（NF-2），使用标准Java API，无平台绑定。
- [X] 确保可扩展性（NF-3），模块化设计，支持插件扩展。
- [X] 确保可维护性（NF-4），遵循SRP单一职责原则，使用设计模式。
- [X] 确保可测试性（NF-5），所有模块单元可测，支持Mock测试。
- [X] 确保可部署性（NF-6），支持打包为JAR或容器化部署。
- [X] 确保可观察性（NF-7），提供日志、抓取统计输出及可选Prometheus指标。

### 异常处理与健壮性任务

- [X] 实现网络异常处理，支持重试、记录错误日志。
- [X] 实现页面异常处理，捕获空页面、DOM错误，跳过但记录原因。
- [X] 实现URL异常处理，使用去重缓存、最大层数限制。
- [X] 实现文件异常处理，支持文件名非法时的fallback处理。
- [X] 实现附件异常处理，下载失败时在Markdown中标记。

### 后续可扩展方向（可选）

- [ ] 开发HTML语言识别与过滤模块，支持仅抓中文内容。
- [ ] 与Elasticsearch/SQLite集成，支持搜索索引。
- [ ] 开发内容摘要与向量化接口，对接LLM系统。
- [ ] 构建分布式爬虫能力，支持任务分发与监控。

## 备注

- 每个任务需遵循设计文档中的模块职责与接口设计，确保模块化与可扩展性。
- 任务完成后，需更新记忆库文件，特别是 `progress.md` 和 `activeContext.md`，以记录当前状态与下一步计划。
