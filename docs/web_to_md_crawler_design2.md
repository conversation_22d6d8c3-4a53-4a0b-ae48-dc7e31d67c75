# 网站爬虫转Markdown结构工具

## 一、项目概述

本项目旨在实现一个基于Java的网页爬虫系统，能够递归抓取指定URL的网页内容，结构化提取信息并转换为Markdown（MD）格式，便于大语言模型（LLM）处理和RAG（检索增强生成）应用。

---

## 二、功能性需求分析

### 2.1 输入参数与配置

系统支持通过命令行参数和配置文件进行灵活配置。命令行参数优先级高于配置文件。

#### 2.1.1 命令行参数 (CLI)

| 参数                | 缩写 | 配置文件Key         | 描述                     | 默认值                |
| ------------------- | ---- | ------------------- | ------------------------ | -------------------- |
| `--url`             | `-u` | `crawler.startUrl`  | 起始抓取URL              | (必须)               |
| `--max-depth`       | `-d` | `crawler.maxDepth`  | 最大递归抓取深度         | `3`                  |
| `--allowed-domains` | `-a` | `crawler.allowedDomains` | 允许抓取的域名白名单（逗号分隔） | 起始URL域名          |
| `--threads`         | `-t` | `crawler.threads`   | 最大并发线程数           | CPU核心数            |
| `--output`          | `-o` | `crawler.outputDir` | Markdown文件输出根目录   | `./output`           |
| `--config`          | `-c` | (N/A)               | 指定配置文件路径         | `./config.properties` |

#### 2.1.2 配置文件

采用 `.properties` 格式，便于Java原生加载。示例如下：

```properties
# 爬虫核心配置
crawler.startUrl=https://example.com
crawler.maxDepth=5
crawler.allowedDomains=example.com,api.example.com
crawler.threads=8
crawler.outputDir=./data

# 网络配置
network.timeout.ms=10000
network.retry.count=3

# 内容处理配置
parser.downloadAttachments=true
parser.attachmentTypes=pdf,docx,txt
```

### 2.2 页面处理功能

1. **递归抓取网页内容，遵循深度限制**
2. **处理非本域名外链，并标记为外链链接信息**
3. **提取并保留网页 meta 信息**
   - title
   - lastModifiedTime（通过 HTTP Header 或 DOM 提取）
   - keywords
   - 其他 meta 标签
4. **结构化提取正文内容：**
   - 标题（h1\~h6）层级保持
   - 段落、列表、引用、代码块保留原格式
   - 表格（table/th/tr）转换为Markdown语法表格
5. **图片处理：**
   - 保留图片说明（alt/title）与原始URL
6. **附件处理：**
   - 下载 word / txt / pdf 文件
   - 在MD中保留附件说明、原始链接、本地文件路径
7. **输出结构：**
   - 按照原始URL的路径结构层次化保存
   - 页面保存为 `.md` 文件
   - 附件及资源按统一目录结构保存
8. **抓取信息追踪：**
   - 每个页面保存原始URL、抓取时间戳、处理状态

---

## 三、非功能性需求分析

| 编号   | 非功能性需求 | 实现策略                     |
| ---- | ------ | ------------------------ |
| NF-1 | 配置灵活性  | 参数可通过 CLI、配置文件加载，支持默认值   |
| NF-2 | 可移植性   | 使用标准Java API，无平台绑定       |
| NF-3 | 可扩展性   | 模块化设计，支持插件扩展             |
| NF-4 | 可维护性   | SRP单一职责原则，使用设计模式         |
| NF-5 | 可测试性   | 所有模块单元可测，支持Mock测试        |
| NF-6 | 可部署性   | 支持打包为JAR，或容器化部署          |
| NF-7 | 可观察性   | 日志、抓取统计输出、可选Prometheus指标 |

---

## 四、模块划分与类设计

### 4.1 核心接口设计

为了实现模块化和可扩展性，定义核心接口。

**`ContentParser` 接口**
```java
public interface ContentParser {
    /**
     * 解析HTML内容，提取页面信息
     * @param htmlContent HTML 字符串
     * @param pageUrl 当前页面URL
     * @return 解析后的页面数据模型 PageData
     */
    PageData parse(String htmlContent, String pageUrl);
}
```

**`HtmlToMarkdownConverter` 接口**
```java
public interface HtmlToMarkdownConverter {
    /**
     * 将 Jsoup Document 转换为 Markdown 字符串
     * @param doc Jsoup 文档对象
     * @return Markdown 格式的字符串
     */
    String convert(Document doc);
}
```

**`StorageManager` 接口**
```java
public interface StorageManager {
    /**
     * 保存页面内容到文件
     * @param pageData 页面数据
     * @return 保存的文件路径
     */
    Path savePage(PageData pageData);

    /**
     * 保存附件
     * @param url 附件URL
     * @param content 附件内容的字节流
     * @return 保存的文件路径
     */
    Path saveAttachment(URL url, InputStream content);
}
```

#### 4.2.1 核心数据模型 (PageData)

`PageData` 是系统内部流转的核心数据结构，封装了从页面抓取和解析出的所有信息。

```java
public class PageData {
    private String url;                 // 原始URL
    private String title;               // 页面标题
    private String markdownContent;     // 转换后的Markdown内容
    private Map<String, String> metadata; // Meta标签 (keywords, description, etc.)
    private List<String> internalLinks; // 内部链接
    private List<String> externalLinks; // 外部链接
    private List<AttachmentInfo> attachments; // 附件信息
    private long crawledTimestamp;      // 抓取时间戳
    // getters and setters
}

public class AttachmentInfo {
    private String originalUrl;         // 附件原始URL
    private String localPath;           // 本地保存路径
    private String description;         // 附件描述 (e.g., from alt text)
    // getters and setters
}
```

### 4.3 总体架构图

```
CrawlCoordinator
 ├── PageFetcher
 ├── LinkExtractor
 ├── ContentParser
 │    ├── HtmlToMarkdownConverter
 │    ├── TableConverter
 │    └── ImageHandler
 ├── FileDownloader
 ├── StorageManager
 └── CrawlReporter
```

### 4.4 类关系图 (UML Class Diagram)

使用 Mermaid 语法描述核心类之间的关系。

```mermaid
classDiagram
    class CrawlCoordinator {
        -ExecutorService threadPool
        -Set<String> visitedUrls
        +submit(url)
        +shutdown()
    }
    class PageFetcher {
        +fetch(url) : String
    }
    class ContentParser {
        -HtmlToMarkdownConverter converter
        +parse(html, url) : PageData
    }
    class HtmlToMarkdownConverter {
        +convert(doc) : String
    }
    class StorageManager {
        -Path rootDir
        +savePage(pageData) : Path
        +saveAttachment(url, content) : Path
    }
    class PageData {
        -String url
        -String title
        -String markdownContent
        -List<String> internalLinks
    }

    CrawlCoordinator --> PageFetcher : uses
    CrawlCoordinator --> ContentParser : uses
    CrawlCoordinator --> StorageManager : uses
    ContentParser --> HtmlToMarkdownConverter : uses
    ContentParser --> PageData : creates
```

### 4.5 核心工作流时序图 (Sequence Diagram)

展示一次完整的页面抓取、处理和存储流程。

```mermaid
sequenceDiagram
    participant User
    participant CrawlCoordinator as CC
    participant PageFetcher as PF
    participant ContentParser as CP
    participant StorageManager as SM

    User->>CC: startCrawl("http://example.com")
    CC->>CC: addUrlToQueue("http://example.com")
    loop For each URL in Queue
        CC->>PF: fetch("http://example.com")
        PF-->>CC: htmlContent
        CC->>CP: parse(htmlContent, "http://example.com")
        CP-->>CC: pageData
        CC->>SM: savePage(pageData)
        SM-->>CC: filePath
        CC->>CC: add new links from pageData to Queue
    end
    CC-->>User: crawlFinished()
```

### 4.6 核心模块职责

#### CrawlCoordinator

- 统一协调任务调度、递归处理、线程池控制、URL去重管理

#### PageFetcher

- 请求URL内容，支持重试、超时控制

#### ContentParser

- 提取meta、正文、结构化内容

#### HtmlToMarkdownConverter

- DOM节点递归解析，转换为MD格式，保留标题层次与表格结构

#### FileDownloader

- 下载附件/图片，限制大小，保存路径统一

#### StorageManager

- 维护目录结构，路径生成、文件写入、命名规范

#### CrawlReporter

- 记录抓取统计、异常记录、生成 crawl\_report.json 报告

---

## 五、异常处理与健壮性设计

| 类型    | 场景           | 策略                         |
| ----- | ------------ | -------------------------- |
| 网络异常  | 连接失败、超时、证书错误 | 支持重试、写入错误日志                |
| 页面异常  | 空页面、DOM错误    | 捕获异常，跳过但记录URL与错误原因         |
| URL异常 | 非法链接、循环抓取    | 去重缓存、最大层数限制                |
| 文件异常  | 写入失败、文件名非法   | fallback 处理（转义或 base64 命名） |
| 附件异常  | 下载失败         | 写入MD中标记“下载失败”              |

---

## 六、性能与伸缩性设计

### 6.1 并发控制

- 使用固定线程池 `ExecutorService`
- 默认线程数 = CPU核心数 \* 2

### 6.2 性能优化点

| 场景    | 优化策略                         |
| ----- | ---------------------------- |
| 网络阻塞  | 限制每域名最大连接数                   |
| 巨型DOM | 提前清洗无用节点（script/style）       |
| 内容写入慢 | BufferedWriter 缓冲写入，提高 IO 性能 |

### 6.3 未来可扩展性

- 多URL批处理任务队列
- 分布式调度框架对接（后续版本）
- 插件式解析策略与渲染模块

---

## 七、存储结构约定

```
output_dir/
 ├── example.com/
 │   ├── index.md
 │   ├── about/team.md
 │   ├── downloads/
 │   │   ├── intro.pdf
 │   │   └── guide.docx
 │   └── images/
 │       └── logo.png
 └── crawl_report.json
```

---

## 八、附加设计建议

### 8.1 Markdown增强规范

- 图片：`![描述](url)` + 本地文件路径
- 表格：完整重建 Markdown 表格语法
- 附件段落示例：

```md
## 附件资源

- [intro.pdf](https://example.com/intro.pdf) 下载于 2025-06-17 12:00:01 保存为 downloads/intro.pdf
```

### 8.2 日志建议

- 支持日志级别：INFO / WARN / ERROR
- 单独输出异常链接清单 `errors.log`

#### 日志格式示例

建议使用结构化日志（如 JSON 格式），便于后续分析。

**`crawler.log` (INFO level):**
```json
{"timestamp": "2025-06-17T23:30:01.123Z", "level": "INFO", "thread": "pool-1-thread-2", "message": "Start fetching URL", "url": "https://example.com/page1"}
{"timestamp": "2025-06-17T23:30:02.456Z", "level": "INFO", "thread": "pool-1-thread-2", "message": "Page parsed successfully, found 5 internal links", "url": "https://example.com/page1"}
{"timestamp": "2025-06-17T23:30:02.789Z", "level": "INFO", "thread": "pool-1-thread-2", "message": "Markdown file saved", "path": "output/example.com/page1.md"}
```

**`errors.log` (WARN/ERROR level):**
```json
{"timestamp": "2025-06-17T23:31:05.321Z", "level": "WARN", "thread": "pool-1-thread-3", "message": "Failed to download attachment, skipping.", "url": "https://example.com/files/report.pdf", "error": "Connection timed out"}
{"timestamp": "2025-06-17T23:32:10.999Z", "level": "ERROR", "thread": "pool-1-thread-1", "message": "Failed to fetch page after 3 retries.", "url": "https://example.com/broken-link", "error": "java.net.UnknownHostException"}
```

---

## 九、测试策略

| 测试类型   | 工具/框架      | 测试重点                                       |
| ---------- | -------------- | ---------------------------------------------- |
| **单元测试** | JUnit 5, Mockito | 核心算法（HTML转MD）、工具类、配置加载的正确性 |
| **集成测试** | Testcontainers | 模块间交互（抓取->解析->存储）、数据库/文件系统交互 |
| **端到端测试** | (无)           | 启动完整爬虫，针对特定网站进行抓取，验证输出结构的完整性和正确性 |
| **性能测试** | JMH            | 关键路径（如DOM解析）的性能基准测试            |

## 十、实现计划

| 阶段 | 内容          | 时间  | 产出                |
| -- | ----------- | --- | ----------------- |
| 1  | 框架搭建、配置加载   | 第1周 | 可运行主程序框架、命令行参数支持  |
| 2  | 抓取 + 转换功能   | 第2周 | 支持单页爬取与Markdown转换 |
| 3  | 递归调度 + 附件处理 | 第3周 | 支持多层链接抓取、附件下载     |
| 4  | 完善存储 + 并发控制 | 第4周 | 并发爬取、层次结构输出完整     |
| 5  | 日志与测试       | 第5周 | 单元测试、异常处理覆盖、报告生成  |

---

## 十一、后续可扩展方向

- HTML语言识别与过滤模块（如仅抓中文内容）
- 与 Elasticsearch / SQLite 集成，支持搜索索引
- 内容摘要与向量化接口，直接对接 LLM 系统
- 分布式爬虫能力构建，支持任务分发与监控

---

如需配套的 Maven 项目结构和代码骨架，请继续说明。

