# AI 网页爬虫系统

一个基于 Spring Boot 的智能网页爬虫系统，支持静态和动态页面抓取，将网页内容转换为 Markdown 格式，并提供完整的 Web 管理界面和 REST API。

## 🚀 核心特性

### 爬虫功能
- **多模式抓取**：支持静态 HTML 和 JavaScript 渲染的动态页面
- **智能解析**：自动提取页面标题、内容、链接和元数据
- **格式转换**：将 HTML 内容转换为结构化的 Markdown 格式
- **文件下载**：支持下载页面中的附件（PDF、DOC、图片等）
- **深度控制**：可配置的递归爬取深度和域名限制
- **并发处理**：多线程并发抓取，支持每域名连接数限制

### Web 服务
- **REST API**：完整的任务管理 API，支持创建、查询、取消任务
- **Web 界面**：直观的任务管理界面，实时显示爬取进度
- **API 文档**：集成 Swagger UI，提供交互式 API 文档
- **任务监控**：实时任务状态跟踪和进度展示
- **异步执行**：后台异步执行爬取任务，不阻塞用户操作

### 技术特性
- **Spring Boot 3.5**：现代化的 Java Web 框架
- **JPA/Hibernate**：数据持久化和事务管理
- **H2 数据库**：嵌入式数据库，开箱即用
- **Playwright**：支持现代 JavaScript 应用的动态页面抓取
- **Prometheus 监控**：应用性能监控和指标收集
- **全面测试**：128 个测试用例，覆盖单元测试、集成测试和端到端测试

## 📋 系统要求

- **Java**: 21 或更高版本
- **Maven**: 3.6 或更高版本
- **内存**: 建议 2GB 以上
- **磁盘**: 根据爬取内容量而定

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd crawler
```

### 2. 编译项目
```bash
mvn clean compile
```

### 3. 运行测试
```bash
mvn test
```

### 4. 启动应用

#### Web 服务模式（推荐）
```bash
# 启动 Spring Boot 应用
mvn spring-boot:run

# 或者使用编译后的 JAR
java -jar target/crawler-1.0-SNAPSHOT.jar
```

访问地址：
- **Web 管理界面**: http://localhost:8080/tasks
- **API 文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health

#### 命令行模式
```bash
# 直接运行爬虫
java -cp target/classes com.talkweb.ai.crawler.CrawlerCli --url https://example.com --max-depth 2 --output ./output
```

## 🏗️ 项目架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Layer     │    │  Service Layer  │    │  Crawler Core   │
│  (Controllers)  │◄──►│  (Task Mgmt)    │◄──►│ (CrawlCoord.)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Swagger UI    │    │   Database      │
│  (API Docs)     │    │  (Task State)   │
│                 │    │                 │
└─────────────────┘    └─────────────────┘
```

### 核心组件

#### 1. 爬虫核心 (Crawler Core)
- **CrawlCoordinator**: 爬取协调器，负责任务调度和URL管理
- **PageFetcher**: 静态页面抓取器，基于 HttpURLConnection
- **DynamicPageFetcher**: 动态页面抓取器，基于 Playwright
- **ContentParser**: 内容解析器，提取页面信息和链接
- **StorageManager**: 存储管理器，负责文件保存和组织
- **FileDownloader**: 文件下载器，处理附件下载

#### 2. Web 服务层 (Web Service)
- **TaskController**: REST API 控制器
- **TaskService**: 任务管理服务
- **Task/TaskDto**: 任务数据模型
- **TaskRepository**: 数据访问层

#### 3. 配置管理 (Configuration)
- **CrawlerConfig**: 爬虫配置类
- **ConfigManager**: 配置管理器
- **application.properties**: Spring Boot 配置

## 📊 数据模型

### Task (任务实体)
```java
@Entity
public class Task {
    private UUID id;              // 任务ID
    private String url;           // 起始URL
    private TaskStatus status;    // 任务状态
    private int progress;         // 进度百分比
    private LocalDateTime startTime;  // 开始时间
    private LocalDateTime endTime;    // 结束时间
    private int maxDepth;         // 最大深度
    private String outputDir;     // 输出目录
    // ... 其他配置字段
}
```

### PageData (页面数据)
```java
public class PageData {
    private String url;                    // 页面URL
    private String title;                  // 页面标题
    private String htmlContent;            // 原始HTML
    private String markdownContent;        // Markdown内容
    private List<String> internalLinks;    // 内部链接
    private List<String> externalLinks;    // 外部链接
    private List<PageData> attachments;    // 附件列表
    private LocalDateTime fetchTimestamp;  // 抓取时间
    // ... 其他元数据
}
```

## 🔧 配置说明

### 应用配置 (application.properties)
```properties
# 服务器配置
server.port=8080

# 数据库配置
spring.datasource.url=jdbc:h2:mem:crawlerdb
spring.jpa.hibernate.ddl-auto=create-drop

# 爬虫配置
crawler.default.max-depth=3
crawler.default.threads=4
crawler.default.connection-timeout=10000
```

### 命令行参数
```bash
--url <URL>                    # 起始URL（必需）
--max-depth <数字>             # 最大递归深度（默认：3）
--threads <数字>               # 线程数（默认：4）
--output <目录>                # 输出目录（默认：./output）
--allowed-domains <域名列表>   # 允许的域名
--enable-dynamic-fetcher       # 启用动态页面抓取
--download-attachments         # 下载附件
--connection-timeout <毫秒>    # 连接超时时间
--read-timeout <毫秒>          # 读取超时时间
```

## 📡 API 接口

### 任务管理 API

#### 创建任务
```http
POST /api/tasks
Content-Type: application/json

{
  "url": "https://example.com",
  "maxDepth": 3,
  "outputDir": "/path/to/output",
  "allowedDomains": ["example.com"],
  "enableDynamicFetcher": true
}
```

#### 查询任务
```http
GET /api/tasks/{taskId}
```

#### 取消任务
```http
POST /api/tasks/{taskId}/cancel
```

#### 获取任务列表
```http
GET /api/tasks?page=0&size=10
```

详细的 API 文档请访问：http://localhost:8080/swagger-ui.html

## 🧪 测试

项目包含全面的测试套件：

### 运行所有测试
```bash
mvn test
```

### 测试覆盖
- **单元测试**: 核心组件功能测试
- **集成测试**: Spring Boot 应用集成测试
- **端到端测试**: 完整爬取流程测试
- **边界测试**: 特殊字符、编码、错误处理等

### 测试统计
- 总测试数：128
- 通过率：100%
- 覆盖范围：核心功能、API接口、异常处理

## 📈 监控和运维

### Prometheus 监控
```bash
# 启用 Prometheus 监控
java -jar crawler.jar --enable-prometheus

# 访问监控指标
curl http://localhost:9090/metrics
```

### 健康检查
```bash
# 应用健康状态
curl http://localhost:8080/actuator/health

# 详细信息
curl http://localhost:8080/actuator/info
```

### 日志配置
日志文件位置：
- 应用日志：`logs/crawler.log`
- 错误日志：`logs/errors.log`

## 🚀 部署指南

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
```bash
# 构建 JAR 包
mvn clean package -DskipTests

# 运行应用
java -jar target/crawler-1.0-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  --server.port=8080
```

### Docker 部署
```dockerfile
FROM openjdk:21-jre-slim
COPY target/crawler-1.0-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 Apache 2.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看 [API 文档](http://localhost:8080/swagger-ui.html)
2. 提交 [Issue](../../issues)
3. 联系开发团队

---

**版本**: 1.0-SNAPSHOT  
**最后更新**: 2025-06-23
